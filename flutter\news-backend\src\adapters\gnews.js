const axios = require('axios');
require('dotenv').config();

const API_KEY = process.env.GNEWS_API_KEY;
const BASE_URL = 'https://gnews.io/api/v4';

async function fetchGnewsArticles({
  topic = 'business',
  lang = 'en',
  country = 'us',
  max = 100, // Увеличиваем до 100 новостей
  searchQuery = ''
} = {}) {
  try {
    const response = await axios.get(`${BASE_URL}/search`, {
      params: {
        q: searchQuery,
        topic,
        lang,
        country,
        max,
        apikey: API_KEY,
        full_content: true, // Запрашиваем полный контент
        in: 'title,description,content' // Ищем во всех полях
      }
    });

    if (!response.data?.articles?.length) {
      console.log('[Gnews] Нет новостей в ответе');
      return [];
    }

    return response.data.articles.map(article => ({
      id: article.url || article.title,
      title: article.title,
      description: article.description,
      content: article.content, // Полный контент
      publishedAt: article.publishedAt,
      source: article.source.name,
      sourceDomain: article.source.url,
      url: article.url,
      imageUrl: article.image,
      // Дополнительные метаданные
      wordCount: article.content.split(/\s+/).length,
      fullText: article.content,
      // Теги из темы и поискового запроса
      tags: [
        topic,
        ...(searchQuery ? searchQuery.split(/\s+/) : [])
      ].filter(Boolean)
    }));
  } catch (error) {
    console.error('[Gnews] Ошибка получения новостей:', error.message);
    return [];
  }
}

// Функция для получения новостей по разным темам
async function fetchAllGnewsTopics() {
  const topics = ['business', 'technology', 'science', 'health', 'entertainment'];
  const newsPromises = topics.map(topic => 
    fetchGnewsArticles({ topic, max: 10 })
  );

  const allNews = await Promise.all(newsPromises);
  return allNews.flat();
}

module.exports = { 
  fetchGnewsArticles,
  fetchAllGnewsTopics
}; 