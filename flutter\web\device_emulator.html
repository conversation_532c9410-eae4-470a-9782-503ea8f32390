<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Flutter App Device Emulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .emulator-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 1400px;
            width: 100%;
            display: flex;
            gap: 30px;
        }

        .device-frame {
            position: relative;
            background: #1a1a1a;
            border-radius: 30px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 20px;
            background: white;
            overflow: hidden;
        }

        .controls {
            min-width: 300px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .control-group h3 {
            margin-bottom: 15px;
            color: #495057;
            font-size: 16px;
            font-weight: 600;
        }

        .device-preset {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 8px;
        }

        .device-preset:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .device-preset.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }

        .device-icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: 600;
            font-size: 14px;
        }

        .device-size {
            font-size: 12px;
            opacity: 0.7;
        }

        .custom-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .custom-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .apply-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .apply-btn:hover {
            background: #0056b3;
        }

        .url-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .load-btn {
            width: 100%;
            padding: 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .load-btn:hover {
            background: #218838;
        }

        .current-size {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
            color: #1976d2;
            font-weight: 500;
        }

        /* Device-specific styles */
        .iphone-14 {
            width: 390px;
            height: 844px;
            background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
        }

        .iphone-14::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 30px;
            background: #000;
            border-radius: 15px;
            z-index: 10;
        }

        .samsung-s23 {
            width: 360px;
            height: 780px;
            background: linear-gradient(145deg, #2a2a2a, #1e1e1e);
        }

        .ipad-air {
            width: 820px;
            height: 1180px;
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
        }

        .desktop {
            width: 1200px;
            height: 800px;
            background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
            border-radius: 10px;
        }

        @media (max-width: 1200px) {
            .emulator-container {
                flex-direction: column;
                align-items: center;
            }
            
            .device-frame {
                transform: scale(0.8);
            }
        }
    </style>
</head>
<body>
    <div class="emulator-container">
        <div class="device-frame" id="deviceFrame">
            <iframe 
                id="deviceScreen" 
                class="device-screen" 
                src="http://localhost:8080"
                title="Flutter App">
            </iframe>
        </div>

        <div class="controls">
            <div class="control-group">
                <h3>🌐 App URL</h3>
                <input 
                    type="url" 
                    id="appUrl" 
                    class="url-input" 
                    value="http://localhost:8080" 
                    placeholder="Enter Flutter app URL">
                <button class="load-btn" onclick="loadApp()">Load App</button>
            </div>

            <div class="control-group">
                <h3>📱 Device Presets</h3>
                
                <div class="device-preset active" onclick="setDevice('iphone-14', 390, 844)">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">iPhone 14</div>
                        <div class="device-size">390 × 844</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('samsung-s23', 360, 780)">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">Samsung Galaxy S23</div>
                        <div class="device-size">360 × 780</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('iphone-se', 375, 667)">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">iPhone SE</div>
                        <div class="device-size">375 × 667</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('mobile-small', 320, 568)">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">Small Mobile</div>
                        <div class="device-size">320 × 568</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('pixel-7', 412, 915)">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">Google Pixel 7</div>
                        <div class="device-size">412 × 915</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('ipad-air', 820, 1180)">
                    <div class="device-icon">📟</div>
                    <div class="device-info">
                        <div class="device-name">iPad Air</div>
                        <div class="device-size">820 × 1180</div>
                    </div>
                </div>

                <div class="device-preset" onclick="setDevice('desktop', 1200, 800)">
                    <div class="device-icon">💻</div>
                    <div class="device-info">
                        <div class="device-name">Desktop</div>
                        <div class="device-size">1200 × 800</div>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h3>🔧 Custom Size</h3>
                <div class="custom-controls">
                    <input type="number" id="customWidth" class="custom-input" placeholder="Width" value="390">
                    <input type="number" id="customHeight" class="custom-input" placeholder="Height" value="844">
                    <button class="apply-btn" onclick="applyCustomSize()">Apply</button>
                </div>
            </div>

            <div class="control-group">
                <h3>🔄 Orientation</h3>
                <div style="display: flex; gap: 10px;">
                    <button class="apply-btn" onclick="rotateDevice()" style="flex: 1;">
                        🔄 Rotate
                    </button>
                    <button class="apply-btn" onclick="refreshApp()" style="flex: 1; background: #17a2b8;">
                        🔄 Refresh
                    </button>
                </div>
            </div>

            <div class="control-group">
                <h3>📏 Current Size</h3>
                <div class="current-size" id="currentSize">390 × 844 px</div>
            </div>
        </div>
    </div>

    <script>
        function setDevice(deviceClass, width, height) {
            const frame = document.getElementById('deviceFrame');
            const screen = document.getElementById('deviceScreen');
            
            // Remove all device classes
            frame.className = 'device-frame';
            
            // Add new device class
            if (deviceClass) {
                frame.classList.add(deviceClass);
            }
            
            // Set dimensions
            frame.style.width = width + 'px';
            frame.style.height = height + 'px';
            
            // Update current size display
            document.getElementById('currentSize').textContent = `${width} × ${height} px`;
            
            // Update custom inputs
            document.getElementById('customWidth').value = width;
            document.getElementById('customHeight').value = height;
            
            // Update active preset
            document.querySelectorAll('.device-preset').forEach(preset => {
                preset.classList.remove('active');
            });
            event.target.closest('.device-preset').classList.add('active');
            
            // Refresh iframe to trigger responsive changes
            const currentSrc = screen.src;
            screen.src = '';
            setTimeout(() => {
                screen.src = currentSrc;
            }, 100);
        }

        function applyCustomSize() {
            const width = parseInt(document.getElementById('customWidth').value);
            const height = parseInt(document.getElementById('customHeight').value);
            
            if (width && height) {
                const frame = document.getElementById('deviceFrame');
                const screen = document.getElementById('deviceScreen');
                
                // Remove device classes for custom size
                frame.className = 'device-frame';
                
                // Set custom dimensions
                frame.style.width = width + 'px';
                frame.style.height = height + 'px';
                
                // Update current size display
                document.getElementById('currentSize').textContent = `${width} × ${height} px`;
                
                // Remove active from presets
                document.querySelectorAll('.device-preset').forEach(preset => {
                    preset.classList.remove('active');
                });
                
                // Refresh iframe
                const currentSrc = screen.src;
                screen.src = '';
                setTimeout(() => {
                    screen.src = currentSrc;
                }, 100);
            }
        }

        function loadApp() {
            const url = document.getElementById('appUrl').value;
            const screen = document.getElementById('deviceScreen');
            screen.src = url;
        }

        function rotateDevice() {
            const frame = document.getElementById('deviceFrame');
            const currentWidth = parseInt(frame.style.width);
            const currentHeight = parseInt(frame.style.height);

            // Swap dimensions
            frame.style.width = currentHeight + 'px';
            frame.style.height = currentWidth + 'px';

            // Update display
            document.getElementById('currentSize').textContent = `${currentHeight} × ${currentWidth} px`;
            document.getElementById('customWidth').value = currentHeight;
            document.getElementById('customHeight').value = currentWidth;

            // Remove active from presets since it's now rotated
            document.querySelectorAll('.device-preset').forEach(preset => {
                preset.classList.remove('active');
            });

            refreshApp();
        }

        function refreshApp() {
            const screen = document.getElementById('deviceScreen');
            const currentSrc = screen.src;
            screen.src = '';
            setTimeout(() => {
                screen.src = currentSrc;
            }, 100);
        }

        // Initialize with iPhone 14
        document.addEventListener('DOMContentLoaded', function() {
            setDevice('iphone-14', 390, 844);
        });
    </script>
</body>
</html>
