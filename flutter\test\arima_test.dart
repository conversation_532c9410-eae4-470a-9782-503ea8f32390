import 'package:flutter_test/flutter_test.dart';
import 'package:finance_ai/utils/arima.dart';
import 'dart:math';

void main() {
  group('ARIMA Model Tests', () {
    test('ARIMA(1,1,1) model initialization', () {
      final model = ArimaModel(p: 1, d: 1, q: 1);
      expect(model.p, equals(1));
      expect(model.d, equals(1));
      expect(model.q, equals(1));
      expect(model.isTrained, equals(false));
    });

    test('ARIMA model with coefficients initialization', () {
      final model = ArimaModel.withCoefficients(
        p: 1,
        d: 1,
        q: 1,
        arCoefficients: [0.7],
        maCoefficients: [0.3],
        intercept: 0.0,
      );
      expect(model.p, equals(1));
      expect(model.d, equals(1));
      expect(model.q, equals(1));
      expect(model.isTrained, equals(true));
    });

    test('ARIMA factory method for ARIMA(1,1,1)', () {
      final model = ArimaModel.arima111();
      expect(model.p, equals(1));
      expect(model.d, equals(1));
      expect(model.q, equals(1));
      expect(model.isTrained, equals(true));
    });

    test('ARIMA model training with simple time series', () {
      final model = ArimaModel(p: 1, d: 1, q: 1);
      final timeSeries = [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0];

      model.train(timeSeries);
      expect(model.isTrained, equals(true));
    });

    test('ARIMA model forecasting with simple time series', () {
      final model = ArimaModel(p: 1, d: 1, q: 1);
      final timeSeries = [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0];

      model.train(timeSeries);
      final forecasts = model.forecast(3);

      expect(forecasts.length, equals(3));

      // For a simple linear trend, we expect forecasts to continue the trend
      // with some variation due to the AR and MA components
      expect(forecasts[0], greaterThan(70.0));

      // The forecast might not always be strictly increasing due to AR and MA components
      // So we'll check that the last forecast is greater than the first value in the time series
      expect(forecasts.last, greaterThan(timeSeries.first));
    });

    test('ARIMA model with non-linear time series', () {
      final model = ArimaModel(p: 1, d: 1, q: 1);
      // Sine wave pattern
      final timeSeries = List.generate(
        20,
        (i) => 50.0 + 30.0 * sin(i / 10.0)
      );

      model.train(timeSeries);
      final forecasts = model.forecast(5);

      expect(forecasts.length, equals(5));

      // For a sine wave, we expect the forecasts to follow the pattern
      // but with some error due to the model's limitations
      for (final forecast in forecasts) {
        expect(forecast, inInclusiveRange(0.0, 100.0));
      }
    });

    test('ARIMA model with random time series', () {
      final model = ArimaModel(p: 1, d: 1, q: 1);
      // Random walk with drift
      final timeSeries = <double>[];
      double value = 50.0;
      for (int i = 0; i < 30; i++) {
        value += (0.5 - (i % 3) * 0.25); // Small drift
        timeSeries.add(value);
      }

      model.train(timeSeries);
      final forecasts = model.forecast(7);

      expect(forecasts.length, equals(7));

      // For a random walk, we expect the forecasts to be reasonable
      // but not necessarily follow a clear pattern
      for (final forecast in forecasts) {
        expect(forecast, inInclusiveRange(40.0, 60.0));
      }
    });

    test('ARIMA model parameters', () {
      final model = ArimaModel.withCoefficients(
        p: 2,
        d: 1,
        q: 2,
        arCoefficients: [0.7, 0.2],
        maCoefficients: [0.3, 0.1],
        intercept: 0.5,
      );

      final params = model.getParameters();

      expect(params['p'], equals(2));
      expect(params['d'], equals(1));
      expect(params['q'], equals(2));
      expect(params['ar_coefficients'], equals([0.7, 0.2]));
      expect(params['ma_coefficients'], equals([0.3, 0.1]));
      expect(params['intercept'], equals(0.5));
    });
  });
}
