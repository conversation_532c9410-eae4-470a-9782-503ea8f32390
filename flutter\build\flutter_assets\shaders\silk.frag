{"sksl": {"entrypoint": "silk_fragment_main", "shader": "// This SkSL shader is autogenerated by spirv-cross.\n\nfloat4 flutter_FragCoord;\n\nuniform vec2 uSize;\nuniform float uTime;\nuniform vec3 uColor;\nuniform float uSpeed;\nuniform float uScale;\nuniform float uRotation;\nuniform float uNoiseIntensity;\n\nvec4 fragColor;\n\nvec2 FLT_flutter_local_FlutterFragCoord()\n{\n    return flutter_FragCoord.xy;\n}\n\nfloat FLT_flutter_local_noise(vec2 texCoord)\n{\n    float G = 2.71828174591064453125;\n    vec2 r = sin(texCoord * G) * G;\n    return fract((r.x * r.y) * (1.0 + texCoord.x));\n}\n\nvec2 FLT_flutter_local_rotateUvs(vec2 uv, float angle)\n{\n    float c = cos(angle);\n    float s = sin(angle);\n    mat2 rot = mat2(vec2(c, -s), vec2(s, c));\n    return rot * uv;\n}\n\nvoid FLT_main()\n{\n    vec2 fragCoord = FLT_flutter_local_FlutterFragCoord();\n    vec2 uv_1 = fragCoord / uSize;\n    vec2 param = fragCoord;\n    float rnd = FLT_flutter_local_noise(param);\n    vec2 param_1 = uv_1 * uScale;\n    float param_2 = uRotation;\n    vec2 rotatedUv = FLT_flutter_local_rotateUvs(param_1, param_2);\n    vec2 tex = rotatedUv * uScale;\n    float tOffset = uSpeed * uTime;\n    tex.y += (0.02999999932944774627685546875 * sin((8.0 * tex.x) - tOffset));\n    float pattern = 0.60000002384185791015625 + (0.4000000059604644775390625 * sin((5.0 * (((tex.x + tex.y) + cos((3.0 * tex.x) + (5.0 * tex.y))) + (0.0199999995529651641845703125 * tOffset))) + sin(20.0 * ((tex.x + tex.y) - (0.100000001490116119384765625 * tOffset)))));\n    vec4 col = (vec4(uColor, 1.0) * vec4(pattern)) - vec4((rnd / 15.0) * uNoiseIntensity);\n    col.w = 1.0;\n    fragColor = col;\n}\n\nhalf4 main(float2 iFragCoord)\n{\n      flutter_FragCoord = float4(iFragCoord, 0, 0);\n      FLT_main();\n      return fragColor;\n}\n", "stage": 1, "uniforms": [{"array_elements": 0, "bit_width": 32, "columns": 1, "location": 0, "name": "uSize", "rows": 2, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 1, "name": "uTime", "rows": 1, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 2, "name": "uColor", "rows": 3, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 3, "name": "uSpeed", "rows": 1, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 4, "name": "uScale", "rows": 1, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 5, "name": "uRotation", "rows": 1, "type": 10}, {"array_elements": 0, "bit_width": 32, "columns": 1, "location": 6, "name": "uNoiseIntensity", "rows": 1, "type": 10}]}}