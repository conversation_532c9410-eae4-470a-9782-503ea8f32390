{"version": 3, "file": "background-scheduled-task.js", "sourceRoot": "", "sources": ["../../../../src/tasks/background-scheduled-task/background-scheduled-task.ts"], "names": [], "mappings": ";;;;;AAAA,+BAA8C;AAC9C,iDAAkD;AAGlD,+CAA2C;AAC3C,mCAAsC;AACtC,oDAAgD;AAChD,8DAA0D;AAC1D,0DAAkC;AAClC,0DAAsD;AAEtD,MAAM,UAAU,GAAG,IAAA,cAAW,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAEvD,MAAM,WAAY,SAAQ,qBAAY;CAAE;AAExC,MAAM,uBAAuB;IAC3B,OAAO,CAAc;IACrB,EAAE,CAAS;IACX,IAAI,CAAS;IACb,cAAc,CAAM;IACpB,QAAQ,CAAM;IACd,OAAO,CAAO;IACd,WAAW,CAAgB;IAC3B,YAAY,CAAe;IAE3B,YAAY,cAAsB,EAAE,QAAgB,EAAE,OAAqB;QACzE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAG,IAAA,oBAAQ,EAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,4BAAY,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU;QACR,IAAK,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,SAAS,EAAC,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,0BAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACjF,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACjD,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,IAAI,CAAC;gBACH,IAAI,CAAC,WAAW,GAAG,IAAA,oBAAI,EAAC,UAAU,CAAC,CAAC;gBAEpC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACnC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;oBAC3C,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;wBACvC,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,qCAAqC,IAAI,IAAI,MAAM,EAAE,CAAC,CAAA;wBAC7E,gBAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACnB,YAAY,CAAC,OAAO,CAAC,CAAC;wBACtB,MAAM,CAAC,IAAI,CAAC,CAAA;oBACd,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAY,EAAE,EAAE;oBAC9C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;wBACtB,IAAI,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC;4BAC/B,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BACtE,OAAO,OAAO,CAAC,SAAS,CAAC;wBAC3B,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;wBACjC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;wBACpB,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;wBAC7C,OAAO,SAAS,EAAE,QAAQ,CAAC;wBAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;wBAE9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;oBAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;oBACtC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,OAAO,CAAC,SAAS,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpB,OAAO,EAAE,YAAY;oBACrB,IAAI,EAAE,IAAA,cAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;oBAChC,IAAI,EAAE,IAAI,CAAC,cAAc;oBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI;QACF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAA;YAC/C,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,MAAM,iBAAiB,GAAG,GAAG,EAAE;gBAC7B,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBAEpC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;gBAC7B,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC,CAAC;YAEF,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,iBAAiB,EAAE,CAAC;YACtB,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,WAAW;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;IACjC,CAAC;IAED,OAAO;QACL,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAA;YAClD,CAAC,EAAE,IAAI,CAAC,CAAC;YAGT,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;gBACtC,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAEvC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,cAAc;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,0JAA0J,CAAC,CAAC,CAAC;YACvL,CAAC;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,gBAAgB,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAClD,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,MAAM,gBAAgB,GAAG,GAAG,EAAE;gBAC5B,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;gBAC3C,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YACvC,CAAC,CAAC;YAEF,MAAM,UAAU,GAAG,CAAC,OAAoB,EAAE,EAAE;gBAC1C,gBAAgB,EAAE,CAAC;gBACnB,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACrC,CAAC,CAAC;YAEF,MAAM,MAAM,GAAG,CAAC,OAAoB,EAAE,EAAE;gBACtC,gBAAgB,EAAE,CAAC;gBACnB,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC,CAAC;YAC3F,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAEtC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,cAAc;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,EAAE,CAAC,KAAgB,EAAE,GAAmD;QACtE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,GAAG,CAAC,KAAgB,EAAE,GAAmD;QACvE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,KAAgB,EAAE,GAAmD;QACxE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAChC,CAAC;IAEO,aAAa,CAAC,aAAmB,EAAE,SAAqB;QAC9D,MAAM,SAAS,GAAG,IAAI,8BAAa,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAC1E,MAAM,GAAG,GAAgB;YACvB,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE;YACxB,YAAY,EAAE,SAAS,CAAC,KAAK,EAAE;YAC/B,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,SAAS;SACrB,CAAA;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAED,SAAS,gBAAgB,CAAC,GAAW;IACnC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE7B,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;IAC3C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAElC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC9B,IAAI,CAAC,CAAC,MAAM,EAAC,SAAS,EAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC;AAED,kBAAe,uBAAuB,CAAC"}