const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());

// Altcoin Season Index
app.get('/altcoin-index', async (req, res) => {
  try {
    const response = await fetch('https://www.alternative.me/crypto/altcoin-season/');
    const html = await response.text();
    console.log('ALTSEASON HTML START');
    console.log(html.substring(0, 2000)); // выводим первые 2000 символов для анализа
    console.log('ALTSEASON HTML END');
    // Ищем строку вида: 'Altcoin Season Index: <b>XX</b>'
    const match = html.match(/Altcoin Season Index:\s*<b>(\d+)<\/b>/i);
    if (match && match[1]) {
      const index = parseInt(match[1], 10);
      console.log('AltcoinSeasonIndex (parsed):', index);
      res.json({ index });
      return;
    }
    // Альтернативный вариант: ищем по другому паттерну, если структура изменилась
    const altMatch = html.match(/<div class="bigtext">(\d+)<\/div>/i);
    if (altMatch && altMatch[1]) {
      const index = parseInt(altMatch[1], 10);
      console.log('AltcoinSeasonIndex (alt parsed):', index);
      res.json({ index });
      return;
    }
    console.log('AltcoinSeasonIndex: fallback 55');
    res.json({ index: 55 }); // fallback
  } catch (e) {
    console.log('AltcoinSeasonIndex parse error:', e);
    res.json({ index: 55 }); // fallback
  }
});

// Crypto Sentiment Index (mock, можно заменить на реальный парсинг)
app.get('/crypto-sentiment', async (req, res) => {
  // Здесь можно реализовать парсинг или отдавать mock
  res.json({ sentiment: 60 }); // Пример mock-значения
});

const PORT = 4000;
app.listen(PORT, () => console.log(`Proxy backend running on port ${PORT}`)); 