// Конфигурация источников новостей с четким разделением по типам

const NEWS_SOURCES_CONFIG = {
  // 📈 КРИПТОВАЛЮТНЫЕ ИСТОЧНИКИ
  crypto: {
    enabled: true,
    sources: [
      {
        name: 'CryptoCompare',
        adapter: 'cryptocompare',
        priority: 1,
        description: 'Основной источник криптовалютных новостей'
      },
      {
        name: 'Premium Crypto',
        adapter: 'premium-crypto',
        priority: 2,
        description: 'Премиум криптовалютные источники',
        requiresPremium: true
      }
    ],
    keywords: [
      'bitcoin', 'ethereum', 'cryptocurrency', 'blockchain', 'defi',
      'nft', 'altcoin', 'crypto', 'binance', 'coinbase', 'mining'
    ]
  },

  // 🏛️ ТРАДИЦИОННЫЕ ФИНАНСЫ (стоки, политика, экономика)
  traditional: {
    enabled: true,
    sources: [
      {
        name: 'NewsAPI Traditional Finance',
        adapter: 'newsapi-traditional',
        priority: 1,
        description: 'Стоки, политика, экономика через NewsAPI'
      },
      {
        name: 'Premium Finance',
        adapter: 'premium-finance',
        priority: 2,
        description: 'Премиум традиционные финансовые источники',
        requiresPremium: true
      }
    ],
    categories: {
      stocks: {
        keywords: [
          'stock market', 'NYSE', 'NASDAQ', 'S&P 500', 'Dow Jones',
          'earnings report', 'quarterly results', 'IPO', 'merger acquisition'
        ],
        domains: [
          'bloomberg.com', 'reuters.com', 'wsj.com', 'marketwatch.com',
          'cnbc.com', 'yahoo.com', 'seekingalpha.com', 'fool.com'
        ]
      },
      politics: {
        keywords: [
          'trade war', 'sanctions', 'election economy', 'government policy',
          'tax reform', 'regulation', 'geopolitics economy'
        ],
        domains: [
          'reuters.com', 'bloomberg.com', 'wsj.com', 'ft.com',
          'politico.com', 'washingtonpost.com'
        ]
      },
      economics: {
        keywords: [
          'Federal Reserve', 'interest rates', 'inflation', 'GDP',
          'unemployment', 'economic growth', 'recession', 'monetary policy'
        ],
        domains: [
          'bloomberg.com', 'reuters.com', 'wsj.com', 'ft.com',
          'cnbc.com', 'marketwatch.com'
        ]
      }
    }
  }
};

// Функция для получения конфигурации источников
function getSourcesConfig() {
  return NEWS_SOURCES_CONFIG;
}

// Функция для получения всех доменов для традиционных финансов
function getTraditionalFinanceDomains() {
  const config = NEWS_SOURCES_CONFIG.traditional.categories;
  const allDomains = new Set();
  
  Object.values(config).forEach(category => {
    category.domains.forEach(domain => allDomains.add(domain));
  });
  
  return Array.from(allDomains);
}

// Функция для получения всех ключевых слов для традиционных финансов
function getTraditionalFinanceKeywords() {
  const config = NEWS_SOURCES_CONFIG.traditional.categories;
  const allKeywords = new Set();
  
  Object.values(config).forEach(category => {
    category.keywords.forEach(keyword => allKeywords.add(keyword));
  });
  
  return Array.from(allKeywords);
}

// Функция для получения криптовалютных ключевых слов
function getCryptoKeywords() {
  return NEWS_SOURCES_CONFIG.crypto.keywords;
}

// Функция для определения типа новости по содержимому
function categorizeNewsContent(title, content, tags = []) {
  const titleLower = (title || '').toLowerCase();
  const contentLower = (content || '').toLowerCase();
  const tagsLower = tags.map(tag => tag.toLowerCase());
  
  const cryptoKeywords = getCryptoKeywords();
  const traditionalKeywords = getTraditionalFinanceKeywords();
  
  // Проверяем на криптовалютные ключевые слова
  const cryptoMatches = cryptoKeywords.filter(keyword => 
    titleLower.includes(keyword.toLowerCase()) || 
    contentLower.includes(keyword.toLowerCase()) ||
    tagsLower.includes(keyword.toLowerCase())
  );
  
  // Проверяем на традиционные финансовые ключевые слова
  const traditionalMatches = traditionalKeywords.filter(keyword => 
    titleLower.includes(keyword.toLowerCase()) || 
    contentLower.includes(keyword.toLowerCase()) ||
    tagsLower.includes(keyword.toLowerCase())
  );
  
  if (cryptoMatches.length > traditionalMatches.length) {
    return {
      type: 'crypto',
      confidence: cryptoMatches.length / cryptoKeywords.length,
      matches: cryptoMatches
    };
  } else if (traditionalMatches.length > 0) {
    return {
      type: 'traditional',
      confidence: traditionalMatches.length / traditionalKeywords.length,
      matches: traditionalMatches
    };
  }
  
  return {
    type: 'unknown',
    confidence: 0,
    matches: []
  };
}

module.exports = {
  NEWS_SOURCES_CONFIG,
  getSourcesConfig,
  getTraditionalFinanceDomains,
  getTraditionalFinanceKeywords,
  getCryptoKeywords,
  categorizeNewsContent
};
