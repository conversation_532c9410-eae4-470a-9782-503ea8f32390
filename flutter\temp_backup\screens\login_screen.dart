import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../providers/auth_provider.dart';
import 'hyperjump_animation_screen.dart';
import '../widgets/hover_button.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  final bool _isLogin = true; // Login mode
  bool _isPasswordVisible = false;

  late AnimationController _starsController;
  final List<Star> _stars = [];
  final List<Color> _starColors = [
    Colors.white,
    Colors.white.withBlue(240),
    Colors.white.withRed(240),
    Colors.white.withGreen(240),
  ];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона (увеличиваем количество до 200)
    for (int i = 0; i < 200; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 3.0 + 1.0, // Увеличиваем размер звезд
        blinkDuration: (math.Random().nextDouble() * 1.5 + 1.5) * 3000, // Замедляем мерцание в 3 раза
        color: _starColors[math.Random().nextInt(_starColors.length)], // Добавляем разные цвета
      ));
    }

    // Инициализируем контроллер анимации для звезд с замедленной анимацией
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 9000), // Замедляем анимацию в 3 раза (3000 * 3 = 9000)
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _starsController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (_isLogin) {
      // Login
      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (success && mounted) {
        // Переход к анимации гиперпрыжка
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    } else {
      // Register
      final success = await authProvider.register(
        _emailController.text.split('@')[0], // Simple username from email
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (success && mounted) {
        // Переход к анимации гиперпрыжка
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    }
  }

  Future<void> _loginWithSocial(String provider) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    bool success = false;

    // Вызываем соответствующий метод в зависимости от провайдера
    switch (provider) {
      case 'google':
        success = _isLogin
            ? await authProvider.loginWithGoogle()
            : await authProvider.registerWithGoogle();
        break;
      case 'facebook':
        success = _isLogin
            ? await authProvider.loginWithFacebook()
            : await authProvider.registerWithFacebook();
        break;
      case 'linkedin':
        success = _isLogin
            ? await authProvider.loginWithLinkedIn()
            : await authProvider.registerWithLinkedIn();
        break;
    }

    if (success && mounted) {
      // Переход к анимации гиперпрыжка
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Анимированные звезды на фоне
          AnimatedBuilder(
            animation: _starsController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(size.width, size.height),
                painter: StarsPainter(_stars, _starsController.value),
              );
            },
          ),

          // Эффект размытия
          BackdropFilter(
            filter: ui.ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: Container(
              color: Colors.white.withAlpha(25), // 0.1 opacity
            ),
          ),

          // Основной контент
          SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Логотип и название
                    Padding(
                      padding: const EdgeInsets.only(bottom: 60.0),
                      child: Column(
                        children: [
                          // Стильный логотип с более спокойным цветом
                          Text(
                            'TRADER MAKES MONEY',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1.2,
                              color: Colors.cyan.shade100,
                              shadows: [
                                Shadow(
                                  color: Colors.cyan.shade700.withOpacity(0.5),
                                  blurRadius: 10,
                                  offset: Offset(0, 0),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

                    // Форма авторизации
                    Container(
                      width: math.min(400, MediaQuery.of(context).size.width * 0.9),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.75),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 0.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.4),
                            blurRadius: 10,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Заголовок формы
                            Text(
                              'Sign in with email',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.cyan.shade100,
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 300.ms),

                            const SizedBox(height: 24),

                            // Поле для email
                            Text(
                              'Email or Username',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.cyan.shade200,
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 400.ms),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              style: TextStyle(color: Colors.white, fontSize: 16),
                              decoration: InputDecoration(
                                hintText: 'Enter your email',
                                hintStyle: TextStyle(color: Colors.white38),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white30),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white30),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white70),
                                ),
                                filled: true,
                                fillColor: Colors.black45,
                                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                if (!value.contains('@')) {
                                  return 'Please enter a valid email';
                                }
                                return null;
                              },
                            ).animate().fadeIn(duration: 500.ms, delay: 400.ms),

                            const SizedBox(height: 24),

                            // Поле для пароля
                            Text(
                              'Password',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.cyan.shade200,
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 500.ms),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _passwordController,
                              obscureText: !_isPasswordVisible,
                              style: TextStyle(color: Colors.white, fontSize: 16),
                              decoration: InputDecoration(
                                hintText: 'Enter your password',
                                hintStyle: TextStyle(color: Colors.white38),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isPasswordVisible
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    color: Colors.white70,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isPasswordVisible = !_isPasswordVisible;
                                    });
                                  },
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white30),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white30),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: BorderSide(color: Colors.white70),
                                ),
                                filled: true,
                                fillColor: Colors.black45,
                                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your password';
                                }
                                if (value.length < 6) {
                                  return 'Password must be at least 6 characters';
                                }
                                return null;
                              },
                            ).animate().fadeIn(duration: 500.ms, delay: 600.ms),

                            const SizedBox(height: 32),

                            // Кнопка входа
                            SizedBox(
                              width: double.infinity,
                              child: authProvider.isLoading
                                  ? Container(
                                      height: 48,
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade700,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Center(
                                        child: SizedBox(
                                          height: 24,
                                          width: 24,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2,
                                          ),
                                        ),
                                      ),
                                    )
                                  : ElevatedButton(
                                      onPressed: _submit,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue.shade700,
                                        foregroundColor: Colors.white,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        padding: EdgeInsets.symmetric(vertical: 16),
                                      ),
                                      child: Text(
                                        'Sign in',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                            ).animate().fadeIn(duration: 500.ms, delay: 700.ms),

                            const SizedBox(height: 24),

                            // Сообщение об ошибке
                            if (authProvider.error.isNotEmpty)
                              Container(
                                margin: const EdgeInsets.only(top: 8, bottom: 16),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(color: Colors.red.shade200),
                                ),
                                child: Text(
                                  authProvider.error,
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ).animate().fadeIn(duration: 300.ms).shake(hz: 4),

                            // Разделитель
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 24),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Divider(
                                      color: Colors.cyan.shade800,
                                      thickness: 1,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'or continue with',
                                      style: TextStyle(
                                        color: Colors.cyan.shade200,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Divider(
                                      color: Colors.cyan.shade800,
                                      thickness: 1,
                                    ),
                                  ),
                                ],
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 800.ms),

                            // Кнопки социальных сетей
                            Padding(
                              padding: const EdgeInsets.only(top: 8, bottom: 24),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Google
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 12),
                                    child: _SocialButton(
                                      svgAsset: 'assets/images/google_logo.svg',
                                      fallbackIcon: Icons.g_mobiledata,
                                      color: Colors.white,
                                      onPressed: () => _loginWithSocial('google'),
                                    ),
                                  ).animate().fadeIn(duration: 500.ms, delay: 900.ms),

                                  // Facebook
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 12),
                                    child: _SocialButton(
                                      svgAsset: 'assets/images/facebook_logo.svg',
                                      fallbackIcon: Icons.facebook,
                                      color: const Color(0xFF1877F2),
                                      onPressed: () => _loginWithSocial('facebook'),
                                    ),
                                  ).animate().fadeIn(duration: 500.ms, delay: 1000.ms),

                                  // LinkedIn
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 12),
                                    child: _SocialButton(
                                      svgAsset: 'assets/images/linkedin_logo.svg',
                                      fallbackIcon: Icons.link,
                                      color: const Color(0xFF0A66C2),
                                      onPressed: () => _loginWithSocial('linkedin'),
                                    ),
                                  ).animate().fadeIn(duration: 500.ms, delay: 1100.ms),
                                ],
                              ),
                            ),

                            // Ссылка на регистрацию
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Do not have an account?',
                                    style: TextStyle(
                                      color: Colors.cyan.shade200,
                                      fontSize: 14,
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      // Переход на экран регистрации
                                      Navigator.pushNamed(context, '/register');
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(horizontal: 8),
                                      minimumSize: Size.zero,
                                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    child: Text(
                                      'Sign up',
                                      style: TextStyle(
                                        color: Colors.cyan.shade300,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 1200.ms),

                            const SizedBox(height: 16),

                            // Пропустить авторизацию (скрытая кнопка)
                            Opacity(
                              opacity: 0.6,
                              child: TextButton(
                                onPressed: () {
                                  // Переход к анимации гиперпрыжка
                                  Navigator.pushReplacement(
                                    context,
                                    PageRouteBuilder(
                                      pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
                                      transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: child,
                                        );
                                      },
                                      transitionDuration: const Duration(milliseconds: 500),
                                    ),
                                  );
                                },
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                  minimumSize: Size.zero,
                                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                                child: Text(
                                  'Skip authentication',
                                  style: TextStyle(
                                    color: Colors.cyan.shade700,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 1300.ms),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ).animate().slideY(
            begin: 1.0,
            end: 0.0,
            duration: 1.seconds,
            curve: Curves.easeOutQuad,
          ),
        ],
      ),
    );
  }
}

// Виджет кнопки для социальных сетей
class _SocialButton extends StatelessWidget {
  final String svgAsset;
  final IconData fallbackIcon;
  final Color color;
  final VoidCallback onPressed;

  const _SocialButton({
    required this.svgAsset,
    required this.fallbackIcon,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Center(
          child: _buildIcon(),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    // Пытаемся загрузить SVG-изображение
    return SvgPicture.asset(
      svgAsset,
      width: 20,
      height: 20,
      placeholderBuilder: (context) {
        // Если изображение не найдено, используем запасную иконку
        return Icon(
          fallbackIcon,
          color: color,
          size: 20,
        );
      },
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;
  final Color color;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
    this.color = Colors.white,
  });
}

// Класс для представления кометы
class Comet {
  double x;
  double y;
  double speed;
  double angle;
  double tailLength;
  double size;
  double opacity;
  DateTime lastAppearance;
  int nextAppearanceSeconds; // Время до следующего появления в секундах
  bool isActive; // Флаг активности кометы (находится ли она на экране)

  Comet({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.tailLength,
    required this.size,
  }) :
    opacity = 0.0,
    isActive = false,
    lastAppearance = DateTime.now().subtract(const Duration(seconds: 3)),
    nextAppearanceSeconds = 8 + math.Random().nextInt(8); // Инициализируем случайным значением от 8 до 15

  // Генерируем случайное время появления кометы (от 8 до 15 секунд)
  int _getRandomAppearanceTime() {
    return 8 + math.Random().nextInt(8); // от 8 до 15 секунд
  }

  // Генерируем случайное положение кометы, гарантируя, что она будет видна на экране
  void _setRandomPosition(Size canvasSize) {
    // Выбираем случайную сторону для появления кометы
    int side = math.Random().nextInt(4);

    // Гарантируем, что комета будет пересекать видимую часть экрана
    switch (side) {
      case 0: // Слева
        x = -tailLength;
        // Гарантируем, что комета пройдет через видимую часть экрана
        y = canvasSize.height * 0.2 + math.Random().nextDouble() * canvasSize.height * 0.6;
        // Направление вправо с небольшими вариациями, чтобы комета точно пересекла экран
        angle = -math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 1: // Справа
        x = canvasSize.width + tailLength;
        y = canvasSize.height * 0.2 + math.Random().nextDouble() * canvasSize.height * 0.6;
        // Направление влево с небольшими вариациями
        angle = math.pi - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 2: // Сверху
        x = canvasSize.width * 0.2 + math.Random().nextDouble() * canvasSize.width * 0.6;
        y = -tailLength;
        // Направление вниз с небольшими вариациями
        angle = math.pi / 2 - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 3: // Снизу
        x = canvasSize.width * 0.2 + math.Random().nextDouble() * canvasSize.width * 0.6;
        y = canvasSize.height + tailLength;
        // Направление вверх с небольшими вариациями
        angle = -math.pi / 2 - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
    }

    // Устанавливаем оптимальную скорость для пересечения экрана
    // Более быстрая скорость для больших экранов
    double baseDiagonal = math.sqrt(math.pow(canvasSize.width, 2) + math.pow(canvasSize.height, 2));
    speed = baseDiagonal / 150 + math.Random().nextDouble() * 2.0; // Скорость зависит от размера экрана
  }

  void update(Size canvasSize, DateTime now) {
    // Проверяем, должна ли появиться новая комета
    final secondsSinceLastAppearance = now.difference(lastAppearance).inSeconds;

    if (secondsSinceLastAppearance >= nextAppearanceSeconds) {
      // Создаем новую комету
      isActive = true;
      opacity = 0.0;
      _setRandomPosition(canvasSize);
      lastAppearance = now;
      // Устанавливаем время до следующего появления
      nextAppearanceSeconds = _getRandomAppearanceTime();
    }

    if (isActive) {
      // Обновляем позицию кометы
      x += speed * math.cos(angle);
      y += speed * math.sin(angle);

      // Определяем, находится ли комета в пределах видимой части экрана
      bool isInVisibleArea =
          (x + tailLength > 0 && x - tailLength < canvasSize.width) &&
          (y + tailLength > 0 && y - tailLength < canvasSize.height);

      // Быстрое появление при входе в видимую область
      if (isInVisibleArea && opacity < 1.0) {
        opacity = math.min(1.0, opacity + 0.1); // Быстрое появление
      }

      // Быстрое исчезновение при выходе из видимой области
      if (!isInVisibleArea && opacity > 0.0) {
        opacity = math.max(0.0, opacity - 0.1); // Быстрое исчезновение
      }

      // Проверяем, полностью ли комета вышла за пределы экрана с запасом
      if (x < -tailLength * 2 ||
          x > canvasSize.width + tailLength * 2 ||
          y < -tailLength * 2 ||
          y > canvasSize.height + tailLength * 2) {
        isActive = false; // Комета больше не активна
      }
    }
  }

  // Метод для проверки, видима ли комета в данный момент
  bool isVisible() {
    return isActive && opacity > 0.0;
  }
}

// Кастомный painter для отрисовки звезд и кометы
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;
  static final Comet _comet = Comet(
    x: -100, // Начальная позиция за пределами экрана
    y: 100,
    speed: 10.0, // Скорость движения
    angle: math.pi * 0.15, // Угол движения (немного вниз)
    tailLength: 150.0, // Длина хвоста
    size: 3.0, // Размер головы кометы
  );

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Обновляем состояние кометы
    _comet.update(size, DateTime.now());

    // Рисуем звезды
    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды (усиливаем мерцание)
      final opacity = 0.1 + (0.9 - 0.1) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5);

      // Используем цвет звезды вместо белого по умолчанию
      paint.color = star.color.withAlpha((opacity * 255).toInt());

      // Добавляем эффект свечения для больших звезд
      if (star.size > 2.5) {
        // Рисуем свечение
        final glowPaint = Paint()
          ..color = star.color.withAlpha((opacity * 100).toInt())
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          Offset(star.x * size.width, star.y * size.height),
          star.size * 1.8,
          glowPaint,
        );
      }

      // Рисуем саму звезду
      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }

    // Рисуем комету, если она видима
    if (_comet.isVisible()) {
      _drawComet(canvas, _comet);
    }
  }

  // Метод для отрисовки кометы
  void _drawComet(Canvas canvas, Comet comet) {
    // Создаем градиент для хвоста кометы
    final tailGradient = ui.Gradient.linear(
      Offset(comet.x, comet.y),
      Offset(comet.x - comet.tailLength * math.cos(comet.angle),
             comet.y - comet.tailLength * math.sin(comet.angle)),
      [
        Colors.white.withAlpha((comet.opacity * 255).toInt()),
        Colors.white.withAlpha((comet.opacity * 0.8 * 255).toInt()),
        Colors.blue.withAlpha((comet.opacity * 0.6 * 255).toInt()),
        Colors.transparent,
      ],
      [0.0, 0.3, 0.6, 1.0],
    );

    // Рисуем хвост кометы
    final tailPaint = Paint()
      ..shader = tailGradient
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final path = Path();
    path.moveTo(comet.x, comet.y);
    path.lineTo(
      comet.x - comet.tailLength * math.cos(comet.angle),
      comet.y - comet.tailLength * math.sin(comet.angle),
    );

    canvas.drawPath(path, tailPaint);

    // Рисуем свечение вокруг головы кометы
    final glowPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 0.7 * 255).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0);

    canvas.drawCircle(
      Offset(comet.x, comet.y),
      comet.size * 2.5,
      glowPaint,
    );

    // Рисуем голову кометы
    final headPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 255).toInt());

    canvas.drawCircle(
      Offset(comet.x, comet.y),
      comet.size,
      headPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

