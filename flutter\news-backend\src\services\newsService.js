const { fetchAllNewsWithLimits } = require('./newsQueue');
const { deduplicateNews, cleanCacheFromDuplicates } = require('../dedup/deduplicate');
const { analyzeNews, clearAnalysisCache } = require('../ai/aiAgent');
const { filterRelevantNews } = require('../utils/newsFilter');
const { classifyNews, isFinanciallyRelevant } = require('../utils/newsClassifier');
const { sequentialAnalyzer } = require('./sequentialAnalyzer');
const PQueue = require('p-queue').default;
const fs = require('fs');
const path = require('path');
const NEWS_FEED_CACHE = path.join(__dirname, '../newsFeed.json');

// Функция для вычисления схожести заголовков
function calculateTitleSimilarity(title1, title2) {
  if (!title1 || !title2) return 0;

  // Нормализуем заголовки
  const normalize = (str) => str.toLowerCase().replace(/[^\w\s]/g, '').trim();
  const norm1 = normalize(title1);
  const norm2 = normalize(title2);

  if (norm1 === norm2) return 1;

  // Разбиваем на слова
  const words1 = norm1.split(/\s+/).filter(w => w.length > 2);
  const words2 = norm2.split(/\s+/).filter(w => w.length > 2);

  if (words1.length === 0 || words2.length === 0) return 0;

  // Считаем пересечение слов
  const intersection = words1.filter(word => words2.includes(word));
  const union = [...new Set([...words1, ...words2])];

  // Jaccard similarity
  return intersection.length / union.length;
}

// РАДИКАЛЬНАЯ система предотвращения дубликатов
function createNewsFingerprint(news) {
  const crypto = require('crypto');

  // Нормализуем все ключевые поля
  const normalizeText = (text) => (text || '').trim().toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ');

  const urlFingerprint = (news.url || '').trim().toLowerCase().replace(/https?:\/\//, '').replace(/\/$/, '');
  const titleFingerprint = normalizeText(news.title || '');
  const aiTitleFingerprint = normalizeText(news.aiGeneratedTitle || '');
  const sourceFingerprint = normalizeText(news.source || '');
  const contentFingerprint = normalizeText((news.content || news.description || '').slice(0, 300));

  // Создаем составной fingerprint с учётом AI-заголовка
  const fingerprint = `${urlFingerprint}|${titleFingerprint}|${aiTitleFingerprint}|${sourceFingerprint}|${contentFingerprint}`;

  return crypto.createHash('sha256').update(fingerprint).digest('hex');
}

// Глобальный реестр fingerprints для предотвращения дубликатов
const globalNewsRegistry = new Set();

// Функция проверки уникальности новости
function isNewsUnique(news) {
  const fingerprint = createNewsFingerprint(news);

  if (globalNewsRegistry.has(fingerprint)) {
    console.log(`[DUPLICATE-BLOCK] 🚫 Заблокирован дубликат: "${news.title?.slice(0, 50)}..." (fingerprint: ${fingerprint.slice(0, 8)})`);
    return false;
  }

  // Регистрируем новость как уникальную
  globalNewsRegistry.add(fingerprint);
  console.log(`[UNIQUE-REGISTER] ✅ Зарегистрирована уникальная новость: "${news.title?.slice(0, 50)}..." (fingerprint: ${fingerprint.slice(0, 8)})`);
  return true;
}

// Функция для генерации уникального ID новости
function generateUniqueNewsId(news) {
  const fingerprint = createNewsFingerprint(news);
  return `news_${fingerprint.slice(0, 16)}`;
}

// Функция для инициализации реестра из существующего кеша
function initializeNewsRegistry(existingNews) {
  globalNewsRegistry.clear();
  let registeredCount = 0;

  for (const news of existingNews) {
    if (news && news.title) {
      const fingerprint = createNewsFingerprint(news);
      globalNewsRegistry.add(fingerprint);
      registeredCount++;
    }
  }

  console.log(`[REGISTRY-INIT] 📋 Инициализирован реестр уникальности: ${registeredCount} новостей зарегистрировано`);
}

// Функция для очистки реестра (для тестирования)
function clearNewsRegistry() {
  const size = globalNewsRegistry.size;
  globalNewsRegistry.clear();
  console.log(`[REGISTRY-CLEAR] 🧹 Очищен реестр уникальности: удалено ${size} записей`);
}

const queue = new PQueue({ concurrency: 1, interval: 180000, intervalCap: 10 }); // 10 запросов каждые 3 минуты

// Настройки кеша
const MAX_NEWS_CACHE_SIZE = parseInt(process.env.MAX_NEWS_CACHE_SIZE) || 800;

let newsFeed = [];
let periodicNewsTimer = null; // Таймер для периодического обновления новостей
let periodicCacheTimer = null; // Таймер для периодического сохранения кэша

function ensureCacheDirectory() {
  const cacheDir = path.dirname(NEWS_FEED_CACHE);
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
  }
}

function loadNewsFeedCache() {
  try {
    ensureCacheDirectory();
    
    if (fs.existsSync(NEWS_FEED_CACHE)) {
      const data = fs.readFileSync(NEWS_FEED_CACHE, 'utf-8');
      const allNews = JSON.parse(data);
      
      if (!Array.isArray(allNews)) {
        console.error('[CACHE] Ошибка: newsFeed.json не является массивом');
        return [];
      }
      
      if (allNews.length === 0) {
        console.log('[CACHE] Кэш пуст, будет выполнен первичный парсинг');
        return [];
      }

      console.log(`[CACHE] 📦 Загружено ${allNews.length} новостей из файла кэша`);

      // 🧹 ОЧИЩАЕМ КЕША ОТ ДУБЛИКАТОВ ПРИ ЗАГРУЗКЕ
      const cleanedNews = cleanCacheFromDuplicates(allNews);

      // Если количество изменилось, сохраняем очищенный кеш
      if (cleanedNews.length !== allNews.length) {
        console.log(`[CACHE] 💾 Сохраняем очищенный кеш (было: ${allNews.length}, стало: ${cleanedNews.length})`);
        fs.writeFileSync(NEWS_FEED_CACHE, JSON.stringify(cleanedNews, null, 2), 'utf-8');
      }

      const sorted = cleanedNews.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
      const missingSentiment = sorted.filter(n => !n.sentiment).length;
      const missingTitle = sorted.filter(n => !n.aiGeneratedTitle).length;
      const missingSummary = sorted.filter(n => !n.summary).length;

      console.log(`[CACHE] ✅ Загружено ${sorted.length} уникальных новостей из кэша. Без sentiment: ${missingSentiment}, без aiGeneratedTitle: ${missingTitle}, без summary: ${missingSummary}`);
      return sorted;
    } else {
      console.log('[CACHE] Файл кэша не найден, будет создан новый');
      fs.writeFileSync(NEWS_FEED_CACHE, JSON.stringify([], null, 2), 'utf-8');
      return [];
    }
  } catch (e) {
    console.error('[CACHE] Ошибка чтения newsFeed.json:', e.message);
    return [];
  }
}

function saveNewsFeedCache(news) {
  try {
    ensureCacheDirectory();
    
    if (!Array.isArray(news)) {
      console.error('[CACHE] Ошибка: news не является массивом');
      return;
    }
    
    const sortedNews = news
      .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
      .slice(0, MAX_NEWS_CACHE_SIZE); // Используем настраиваемый размер кеша
      
    if (sortedNews.length === 0) {
      console.log('[CACHE] Нет новостей для сохранения');
      return;
    }
    
    const missingSentiment = sortedNews.filter(n => !n.sentiment).length;
    const missingTitle = sortedNews.filter(n => !n.aiGeneratedTitle).length;
    const missingSummary = sortedNews.filter(n => !n.summary).length;
    
    fs.writeFileSync(NEWS_FEED_CACHE, JSON.stringify(sortedNews, null, 2), 'utf-8');
    console.log(`[CACHE] Сохранено ${sortedNews.length} новостей в кэш. Без sentiment: ${missingSentiment}, без aiGeneratedTitle: ${missingTitle}, без summary: ${missingSummary}`);
  } catch (e) {
    console.error('[CACHE] Ошибка записи newsFeed.json:', e.message);
  }
}

newsFeed = loadNewsFeedCache();
console.log(`[CACHE] Максимальный размер кеша новостей: ${MAX_NEWS_CACHE_SIZE}`);

// Инициализируем реестр уникальности из существующего кеша
initializeNewsRegistry(newsFeed);

// Запускаем периодическое сохранение кэша каждые 5 минут
function startPeriodicCacheSaving() {
  if (periodicCacheTimer) {
    clearInterval(periodicCacheTimer);
  }

  periodicCacheTimer = setInterval(() => {
    if (newsFeed.length > 0) {
      console.log('[CACHE] Периодическое сохранение кэша...');
      saveNewsFeedCache(newsFeed);
    }
  }, 5 * 60 * 1000); // 5 минут

  console.log('[CACHE] Периодическое сохранение кэша каждые 5 минут активировано');
}

startPeriodicCacheSaving();

function refreshNewsFeed() {
  newsFeed = loadNewsFeedCache();
}

// Функция для подсчета слов в тексте
function countWords(text) {
  if (!text || typeof text !== 'string') return 0;

  // Удаляем HTML теги, лишние пробелы и символы
  const cleanText = text
    .replace(/<[^>]*>/g, ' ') // Удаляем HTML теги
    .replace(/[^\w\s]/g, ' ') // Заменяем знаки препинания на пробелы
    .replace(/\s+/g, ' ') // Заменяем множественные пробелы на одинарные
    .trim();

  if (!cleanText) return 0;

  // Считаем слова
  return cleanText.split(' ').filter(word => word.length > 0).length;
}

// Функция для проверки качества контента
function hasQualityContent(news, minWords = 500) {
  const content = news.content || news.description || '';
  const wordCount = countWords(content);

  console.log(`[QUALITY] "${news.title?.slice(0, 50)}..." - ${wordCount} words (min: ${minWords})`);

  return wordCount >= minWords;
}

async function analyzeAndCacheNews(newsList, broadcastCallback = null) {
  if (!Array.isArray(newsList) || newsList.length === 0) {
    console.log('[PROCESS] Нет новостей для анализа');
    saveNewsFeedCache(newsFeed);
    return;
  }

  let addedCount = 0;
  for (const news of newsList) {
    if (!news || !news.title) {
      console.log('[SKIP] Пропущена некорректная новость');
      continue;
    }

    // РАДИКАЛЬНАЯ проверка уникальности через fingerprint
    if (!isNewsUnique(news)) {
      continue; // Пропускаем дубликат
    }

    try {
      // Сначала проверяем финансовую релевантность
      if (!isFinanciallyRelevant(news)) {
        console.log('[SKIP] Новость не релевантна для финансовых рынков:', news.title);
        continue;
      }

      // Классифицируем новость
      const classification = classifyNews(news);
      console.log(`[CLASSIFY] ${news.title} -> ${classification.primaryCategory} (${(classification.confidence * 100).toFixed(1)}%)`);

      const analyzed = await queue.add(() => analyzeNews(news));
      if (!analyzed) {
        console.log('[SKIP] Новость не прошла анализ:', news.title);
        continue;
      }

      // Добавляем время сохранения в кэш
      analyzed.cachedAt = new Date().toISOString();

      console.log('[DEBUG] analyzeNews result:', analyzed);
      
      // --- Хранение истории анализа ---
      const prev = newsFeed.find(n => n.id === news.id || n.url === news.url);
      let analysisHistory = [];
      if (prev) {
        analysisHistory = prev.analysisHistory || [];
        analysisHistory.push({
          sentiment: prev.sentiment,
          aiGeneratedTitle: prev.aiGeneratedTitle,
          summary: prev.summary,
          analyzedAt: prev.analyzedAt || prev.publishedAt,
        });
        const idx = newsFeed.indexOf(prev);
        if (idx !== -1) newsFeed.splice(idx, 1);
      }

      // Гарантируем, что title всегда равен aiGeneratedTitle (если он есть)
      const finalTitle = analyzed.aiGeneratedTitle && analyzed.aiGeneratedTitle.trim().length > 0
        ? analyzed.aiGeneratedTitle
        : analyzed.title;

      // Генерируем уникальный ID для предотвращения дубликатов
      const uniqueId = generateUniqueNewsId(analyzed);

      const newsFeedEntry = {
        ...analyzed,
        id: uniqueId, // Перезаписываем ID на уникальный
        title: finalTitle,
        originalTitle: analyzed.title,
        analysisHistory,
        analyzedAt: new Date().toISOString(),
        // Добавляем данные классификации
        category: classification.primaryCategory,
        categoryConfidence: classification.confidence,
        categoryScores: classification.scores,
        classificationTags: classification.tags,
      };

      // НОВАЯ ЛОГИКА: Сначала добавляем в кэш, потом в ленту
      console.log(`[CACHE] 💾 Сохраняем новость в кэш: ${finalTitle}`);

      // Добавляем в newsFeed (кэш)
      newsFeed.push(newsFeedEntry);

      // Сразу сохраняем кэш
      saveNewsFeedCache(newsFeed);
      console.log(`[CACHE] ✅ Новость сохранена в кэш`);

      // Теперь отправляем в ленту (клиентам)
      console.log(`[FEED] 📡 Отправляем новость в ленту: ${finalTitle}`);

      addedCount++;
      console.log(`[PROCESS] ✅ Новость проанализирована: ${finalTitle} (Sentiment: ${analyzed.sentiment})`);

      if (broadcastCallback && typeof broadcastCallback === 'function') {
        broadcastCallback(newsFeedEntry, 'news-added');
        console.log(`[FEED] ✅ Новость отправлена клиентам`);
      }
    } catch (e) {
      console.error('[AI] Ошибка анализа:', news.title, e.message);
    }
  }

  // Финальная статистика
  console.log(`[PROCESS] 🎉 Фоновый анализ завершён. Добавлено: ${addedCount}, Всего новостей: ${newsFeed.length}`);
}

async function startBackgroundParseAndAnalyze(broadcastCallback = null) {
  console.log('[PROCESS] Запуск фонового анализа новостей...');
  clearAnalysisCache();
  try {
    const rawNews = await fetchAllNewsWithLimits();

    // Фильтруем по качеству контента (минимум 500 слов)
    const newsWithQualityContent = rawNews.filter(news => {
      if (!news.content && !news.description) {
        console.log(`[SKIP] Нет контента: ${news.title}`);
        return false;
      }

      if (!hasQualityContent(news, 500)) {
        console.log(`[SKIP] Недостаточно слов: ${news.title}`);
        return false;
      }

      return true;
    });

    console.log(`[FILTER] Отфильтровано ${newsWithQualityContent.length} из ${rawNews.length} новостей по качеству контента`);

    // ОТКЛЮЧАЕМ старую дедупликацию - теперь используем fingerprint систему
    // const uniqueNews = deduplicateNews(newsWithQualityContent);
    const filteredNews = filterRelevantNews(newsWithQualityContent);
    await analyzeAndCacheNews(filteredNews, broadcastCallback);
    scheduleNextNewsUpdate(broadcastCallback);
    return { started: true, total: filteredNews.length };
  } catch (error) {
    console.error('[PROCESS] ❌ Ошибка при анализе новостей:', error.message);
    scheduleNextNewsUpdate(broadcastCallback);
    throw error;
  }
}

// Новая функция для запуска последовательного анализа
async function startSequentialAnalysis(broadcastCallback = null) {
  console.log('[PROCESS] 🚀 Запуск последовательного анализа новостей...');
  clearAnalysisCache();

  try {
    // Получаем все новости
    const rawNews = await fetchAllNewsWithLimits();

    // Фильтруем по качеству контента (минимум 500 слов)
    const newsWithQualityContent = rawNews.filter(news => {
      if (!news.content && !news.description) {
        console.log(`[SKIP] Нет контента: ${news.title}`);
        return false;
      }

      if (!hasQualityContent(news, 500)) {
        console.log(`[SKIP] Недостаточно слов: ${news.title}`);
        return false;
      }

      return true;
    });

    console.log(`[FILTER] Отфильтровано ${newsWithQualityContent.length} из ${rawNews.length} новостей по качеству контента`);

    // ОТКЛЮЧАЕМ старую дедупликацию - теперь используем fingerprint систему
    // const uniqueNews = deduplicateNews(newsWithQualityContent);
    const filteredNews = filterRelevantNews(newsWithQualityContent);

    // Объединяем с существующими новостями для анализа
    const allNews = [...newsFeed, ...filteredNews];

    // Настраиваем обработчики событий
    sequentialAnalyzer.on('newsAnalyzed', (data) => {
      // Обновляем новость в кэше
      const existingIndex = newsFeed.findIndex(n =>
        n.id === data.news.id || n.url === data.news.url
      );

      if (existingIndex >= 0) {
        newsFeed[existingIndex] = data.news;
      } else {
        newsFeed.push(data.news);
      }

      // Сохраняем кэш
      saveNewsFeedCache(newsFeed);

      // Уведомляем клиентов
      if (broadcastCallback) {
        broadcastCallback(data.news, 'news-analyzed');
      }
    });

    sequentialAnalyzer.on('analysisCompleted', (data) => {
      console.log('[PROCESS] ✅ Последовательный анализ завершен');
      if (broadcastCallback) {
        broadcastCallback(data, 'analysis-completed');
      }
    });

    // Запускаем последовательный анализ
    await sequentialAnalyzer.startSequentialAnalysis(allNews, broadcastCallback);

    return {
      started: true,
      total: allNews.length,
      mode: 'sequential'
    };

  } catch (error) {
    console.error('[PROCESS] ❌ Ошибка при последовательном анализе:', error.message);
    throw error;
  }
}

// Функция для планирования следующего обновления новостей
function scheduleNextNewsUpdate(broadcastCallback = null) {
  // Очищаем предыдущий таймер, если он существует
  if (periodicNewsTimer) {
    clearTimeout(periodicNewsTimer);
  }

  // Планируем следующий запуск через 10 минут (600000 мс)
  periodicNewsTimer = setTimeout(async () => {
    console.log('[SCHEDULER] Запуск периодического обновления новостей...');
    try {
      await startBackgroundParseAndAnalyze(broadcastCallback);
    } catch (error) {
      console.error('[SCHEDULER] Ошибка периодического обновления:', error.message);
    }
  }, 10 * 60 * 1000); // 10 минут

  console.log('[SCHEDULER] Следующее обновление новостей запланировано через 10 минут');
}

// Функция для остановки периодических обновлений
function stopPeriodicNewsUpdates() {
  if (periodicNewsTimer) {
    clearTimeout(periodicNewsTimer);
    periodicNewsTimer = null;
    console.log('[SCHEDULER] Периодические обновления новостей остановлены');
  }

  if (periodicCacheTimer) {
    clearInterval(periodicCacheTimer);
    periodicCacheTimer = null;
    console.log('[CACHE] Периодическое сохранение кэша остановлено');
  }
}

module.exports = {
  fetchAllNewsWithLimits,
  deduplicateNews,
  analyzeNews,
  filterRelevantNews,
  loadNewsFeedCache,
  saveNewsFeedCache,
  startBackgroundParseAndAnalyze,
  startSequentialAnalysis,
  refreshNewsFeed,
  stopPeriodicNewsUpdates,
  newsFeed,
  sequentialAnalyzer,
  countWords,
  hasQualityContent,
  clearNewsRegistry,
  initializeNewsRegistry,
  createNewsFingerprint,
  isNewsUnique
};