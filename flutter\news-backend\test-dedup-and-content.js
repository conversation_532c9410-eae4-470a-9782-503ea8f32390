// Тестовый скрипт для проверки дедупликации и полноты контента
const { deduplicateNews } = require('./src/dedup/deduplicate');
const { analyzeNews } = require('./src/ai/aiAgent');

async function testDedupAndContent() {
  console.log('🔧 Testing Deduplication and Content Completeness\n');
  console.log('=' .repeat(80));
  console.log('🎯 Testing:');
  console.log('   • Improved deduplication logic');
  console.log('   • Full content preservation in AI rewriting');
  console.log('   • Stricter duplicate detection');
  console.log('   • Content length validation');
  console.log('=' .repeat(80));

  try {
    console.log('\n📊 PHASE 1: Testing Deduplication');
    console.log('─'.repeat(50));
    
    // Создаем тестовые новости с дубликатами
    const testNews = [
      {
        id: '1',
        title: 'Bitcoin Stability at $10.5K Fuels Altcoin Momentum in Evolving Crypto Market',
        description: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens.',
        content: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens. The market is witnessing diversification as these altcoins gain traction, supported by technological advancements and clear use cases. Investors are advised to balance optimism with thorough research to navigate the volatile market effectively.',
        source: 'coinotag',
        url: 'https://coinotag.com/bitcoin-stability-1',
        publishedAt: '2025-06-16T01:13:39.000Z'
      },
      {
        id: '2',
        title: 'Bitcoin Stability at $10.5K Fuels Altcoin Momentum in Evolving Crypto Market',
        description: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens.',
        content: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens. The market is witnessing diversification as these altcoins gain traction, supported by technological advancements and clear use cases. Investors are advised to balance optimism with thorough research to navigate the volatile market effectively.',
        source: 'coinotag',
        url: 'https://coinotag.com/bitcoin-stability-1', // Тот же URL
        publishedAt: '2025-06-16T01:13:39.000Z'
      },
      {
        id: '3',
        title: 'Bitcoin Stability at $10.5K Drives Altcoin Growth in Crypto Markets',
        description: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens.',
        content: 'Bitcoin price stability around $10,500 is fostering a shift in investor focus toward altcoins like Aave, Bitcoin Cash, and OKB, driven by the growth of DeFi and utility tokens. The market is witnessing diversification as these altcoins gain traction, supported by technological advancements and clear use cases.',
        source: 'coinotag',
        url: 'https://coinotag.com/bitcoin-stability-2',
        publishedAt: '2025-06-16T01:15:39.000Z'
      },
      {
        id: '4',
        title: 'Middle East Tensions Stabilize Bitcoin at $105K Amid Market Volatility',
        description: 'Israeli airstrikes on Iranian nuclear facilities have intensified Middle East tensions, impacting global markets while Bitcoin demonstrates resilience near $105K.',
        content: 'Israeli airstrikes on Iranian nuclear facilities have intensified Middle East tensions, impacting global markets while Bitcoin demonstrates resilience near $105K. The conflict has increased volatility across risk assets, with particular concern about potential disruptions to critical oil shipping routes. Market analysts suggest that prolonged geopolitical instability could impact crypto prices, though Bitcoin has shown relative stability during such events. Investors are closely monitoring developments as the situation unfolds, with many viewing Bitcoin as a potential safe-haven asset during times of geopolitical uncertainty.',
        source: 'coindesk',
        url: 'https://coindesk.com/middle-east-tensions-1',
        publishedAt: '2025-06-16T00:50:23.000Z'
      },
      {
        id: '5',
        title: 'Middle East Tensions Stabilize Bitcoin at $105K Amid Market Volatility',
        description: 'Israeli airstrikes on Iranian nuclear facilities have intensified Middle East tensions, impacting global markets while Bitcoin demonstrates resilience near $105K.',
        content: 'Israeli airstrikes on Iranian nuclear facilities have intensified Middle East tensions, impacting global markets while Bitcoin demonstrates resilience near $105K. The conflict has increased volatility across risk assets, with particular concern about potential disruptions to critical oil shipping routes.',
        source: 'coindesk',
        url: 'https://coindesk.com/middle-east-tensions-2', // Другой URL, но похожий контент
        publishedAt: '2025-06-16T00:52:23.000Z'
      }
    ];

    console.log(`\n🔍 Input: ${testNews.length} news items`);
    testNews.forEach((news, index) => {
      console.log(`   ${index + 1}. "${news.title.slice(0, 50)}..." (${news.source})`);
    });

    const uniqueNews = deduplicateNews(testNews);
    
    console.log(`\n✅ Output: ${uniqueNews.length} unique news items`);
    uniqueNews.forEach((news, index) => {
      console.log(`   ${index + 1}. "${news.title.slice(0, 50)}..." (${news.source})`);
    });

    console.log('\n📊 PHASE 2: Testing Content Preservation');
    console.log('─'.repeat(50));
    
    if (uniqueNews.length > 0) {
      const testNewsItem = uniqueNews[0];
      const originalLength = testNewsItem.content.length;
      
      console.log(`\n🔍 Testing AI rewriting for: "${testNewsItem.title.slice(0, 50)}..."`);
      console.log(`📏 Original content length: ${originalLength} characters`);
      console.log(`📝 Original content preview: "${testNewsItem.content.slice(0, 200)}..."`);
      
      console.log('\n⏳ Analyzing with AI agent...');
      const startTime = Date.now();
      
      try {
        const analyzedNews = await analyzeNews(testNewsItem);
        const endTime = Date.now();
        
        const rewrittenLength = analyzedNews.rewrittenContent ? analyzedNews.rewrittenContent.length : 0;
        const lengthRatio = originalLength > 0 ? (rewrittenLength / originalLength) : 0;
        const lengthDifference = Math.abs(originalLength - rewrittenLength);
        const lengthPercentDiff = originalLength > 0 ? (lengthDifference / originalLength) * 100 : 0;
        
        console.log(`\n✅ AI Analysis completed in ${endTime - startTime}ms`);
        console.log(`📏 Rewritten content length: ${rewrittenLength} characters`);
        console.log(`📊 Length ratio: ${(lengthRatio * 100).toFixed(1)}%`);
        console.log(`📈 Length difference: ${lengthDifference} characters (${lengthPercentDiff.toFixed(1)}%)`);
        
        if (analyzedNews.rewrittenContent) {
          console.log(`📝 Rewritten content preview: "${analyzedNews.rewrittenContent.slice(0, 200)}..."`);
        }
        
        // Проверяем качество перефразировки
        if (lengthRatio >= 0.85 && lengthRatio <= 1.15) {
          console.log('✅ Content length preserved well (85-115% of original)');
        } else if (lengthRatio >= 0.70 && lengthRatio <= 1.30) {
          console.log('⚠️  Content length acceptable (70-130% of original)');
        } else {
          console.log('❌ Content length significantly changed');
        }
        
        if (analyzedNews.rewrittenContent && analyzedNews.rewrittenContent.length > 100) {
          console.log('✅ Rewritten content is substantial');
        } else {
          console.log('❌ Rewritten content is too short or missing');
        }
        
        console.log(`\n📊 Additional Analysis Results:`);
        console.log(`   • AI Generated Title: "${analyzedNews.aiGeneratedTitle || 'N/A'}"`);
        console.log(`   • Sentiment: ${analyzedNews.sentiment || 'N/A'}`);
        console.log(`   • Summary length: ${analyzedNews.summary ? analyzedNews.summary.length : 0} characters`);
        
      } catch (error) {
        console.error(`❌ AI Analysis failed: ${error.message}`);
      }
    }

    console.log('\n📊 PHASE 3: Testing Multiple News Analysis');
    console.log('─'.repeat(50));
    
    console.log(`\n🔍 Analyzing ${uniqueNews.length} unique news items...`);
    
    for (let i = 0; i < Math.min(uniqueNews.length, 2); i++) {
      const newsItem = uniqueNews[i];
      console.log(`\n📰 News ${i + 1}: "${newsItem.title.slice(0, 40)}..."`);
      console.log(`📏 Original: ${newsItem.content.length} chars`);
      
      try {
        const analyzed = await analyzeNews(newsItem);
        const rewrittenLen = analyzed.rewrittenContent ? analyzed.rewrittenContent.length : 0;
        const ratio = newsItem.content.length > 0 ? (rewrittenLen / newsItem.content.length) : 0;
        
        console.log(`📝 Rewritten: ${rewrittenLen} chars (${(ratio * 100).toFixed(1)}%)`);
        console.log(`🎯 Sentiment: ${analyzed.sentiment || 'N/A'}`);
        
      } catch (error) {
        console.log(`❌ Analysis failed: ${error.message}`);
      }
      
      // Небольшая задержка между анализами
      if (i < uniqueNews.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log('\n🎉 DEDUPLICATION AND CONTENT TEST COMPLETED!');
    console.log('=' .repeat(80));
    console.log('📊 Summary:');
    console.log(`   ✅ Deduplication: ${testNews.length} → ${uniqueNews.length} news items`);
    console.log('   ✅ Improved duplicate detection with URL checking');
    console.log('   ✅ Stricter similarity thresholds');
    console.log('   ✅ Enhanced content preservation in AI rewriting');
    console.log('   ✅ Better length validation and monitoring');
    
    console.log('\n🎯 Key Improvements:');
    console.log('   • URL-based duplicate detection');
    console.log('   • Title similarity threshold: 15%');
    console.log('   • Content similarity threshold: 10%');
    console.log('   • Increased max text length: 15,000 chars');
    console.log('   • Enhanced AI prompts for full content preservation');
    console.log('   • Better text truncation with sentence boundaries');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Запускаем тест
if (require.main === module) {
  testDedupAndContent().catch(error => {
    console.error('❌ Deduplication and content test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testDedupAndContent
};
