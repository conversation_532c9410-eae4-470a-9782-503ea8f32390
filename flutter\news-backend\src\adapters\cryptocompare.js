const axios = require('axios');
const { Readability } = require('@mozilla/readability');
const { JSDOM } = require('jsdom');
const cheerio = require('cheerio');
require('dotenv').config();

const API_KEY = process.env.CRYPTOCOMPARE_API_KEY;
const BASE_URL = 'https://min-api.cryptocompare.com/data/v2/news/';

async function extractFullContent(url) {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
      },
      timeout: 10000,
    });

    const $ = cheerio.load(response.data);
    
    // Удаляем ненужные элементы
    $('script, style, iframe, nav, footer, header, .ad, .advertisement, .social-share, .comments').remove();
    
    // Создаем DOM для Readability
    const dom = new JSDOM($.html());
    const reader = new Readability(dom.window.document);
    const article = reader.parse();
    
    if (article && article.textContent) {
      return {
        content: article.textContent.trim(),
        title: article.title,
        excerpt: article.excerpt,
        byline: article.byline,
        length: article.textContent.length
      };
    }
    
    // Fallback: если Readability не сработал, пробуем найти основной контент
    const mainContent = $('article, .article, .post, .content, .main-content, #content, .entry-content')
      .first()
      .text()
      .trim();
      
    if (mainContent) {
      return {
        content: mainContent,
        title: $('title').text(),
        length: mainContent.length
      };
    }
    
    return null;
  } catch (e) {
    console.error(`[CryptoCompare] Ошибка извлечения контента для ${url}:`, e.message);
    return null;
  }
}

async function fetchCryptoCompareNews() {
  try {
    const response = await axios.get(BASE_URL, {
      params: { 
        lang: 'EN',
        api_key: API_KEY
      },
    });

    if (!response.data?.Data?.length) {
      console.log('[CryptoCompare] Нет новостей в ответе');
      return [];
    }

    const newsPromises = response.data.Data.map(async item => {
      try {
        // Получаем полный контент
        const fullContent = await extractFullContent(item.url);
        
        if (!fullContent) {
          console.log(`[CryptoCompare] Не удалось получить полный контент для: ${item.title}`);
          return null;
        }


        
        return {
          id: item.id,
          title: fullContent.title || item.title,
          description: item.body,
          publishedAt: new Date(item.published_on * 1000).toISOString(),
          source: item.source,
          url: item.url,
          tags: [],
          content: fullContent.content,
          imageUrl: item.imageurl,
          // Специфичные поля CryptoCompare
          coins: item.coins || [],
          sourceInfo: item.source_info || {},
          // Дополнительные поля из полного контента
          author: fullContent.byline,
          excerpt: fullContent.excerpt,
          wordCount: fullContent.length
        };
      } catch (error) {
        console.error(`[CryptoCompare] Ошибка обработки новости "${item.title}":`, error.message);
        return null;
      }
    });

    const newsResults = await Promise.all(newsPromises);
    const validNews = newsResults.filter(item => item !== null);
    
    console.log('[CryptoCompare] Статистика:', {
      получено: response.data.Data.length,
      сПолнымКонтентом: validNews.length,
      пропущено: response.data.Data.length - validNews.length
    });

    return validNews;
  } catch (e) {
    console.error('[CryptoCompare] Ошибка получения новостей:', e.message);
    return [];
  }
}

module.exports = { fetchCryptoCompareNews }; 