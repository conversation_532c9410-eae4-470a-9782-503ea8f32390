# 📰 Техническое Описание Новостной Логики

## 🔄 Общий Принцип Работы

Наша новостная система работает как **конвейер обработки данных** с несколькими этапами:

1. **Сбор** → 2. **Парсинг** → 3. **Фильтрация** → 4. **AI Анализ** → 5. **Кэширование** → 6. **Доставка**

Каждый этап имеет свои механизмы проверки качества, обработки ошибок и fallback стратегии.

---

## 📡 Этап 1: Сбор Новостей (Data Collection)

### Как это работает:
Система автоматически опрашивает 12+ источников новостей каждые 10-15 минут. У нас есть специальный планировщик (newsScheduler.js), который управляет этим процессом.

### Источники данных:
- **RSS фиды**: CoinDesk, Cointelegraph, The Block
- **API endpoints**: NewsAPI, CryptoCompare
- **Web scraping**: Для источников без API

### Rate Limiting:
Каждый источник имеет свои лимиты запросов. Мы отслеживаем количество запросов в кэше (NodeCache) и не превышаем 3 запроса в 5-минутном окне для каждого источника.

### Пример логики:
```
Планировщик запускается → Проверяет rate limits → Делает запросы к источникам → 
Получает сырые данные → Передает на следующий этап
```

---

## 🔍 Этап 2: Парсинг Контента (Content Parsing)

### Что происходит:
Сырые данные (HTML, RSS, JSON) преобразуются в структурированный формат. Мы используем библиотеку `unfluff.js` для извлечения чистого текста из HTML.

### Извлекаемые данные:
- **Заголовок** (title)
- **Описание** (description) 
- **Полный текст** (content)
- **Изображение** (imageUrl)
- **Дата публикации** (publishedAt)
- **Источник** (source)
- **URL** оригинальной статьи

### Обработка ошибок:
Если парсинг не удался, система пытается извлечь хотя бы базовую информацию (заголовок + описание) из RSS или API данных.

---

## ✅ Этап 3: Фильтрация и Валидация

### 3.1 Проверка Качества Контента
Система проверяет каждую новость на соответствие минимальным требованиям:
- **Минимум 500 слов** в тексте статьи
- **Наличие заголовка** (больше 10 символов)
- **Наличие описания** (больше 50 символов)
- **Валидный URL**

### 3.2 Дедупликация
Создается уникальный "отпечаток" (fingerprint) для каждой новости:
- Берутся первые 5 значимых слов из заголовка
- Добавляется префикс источника и дата
- Проверяется в глобальном реестре новостей

### 3.3 Финансовая Релевантность
Система проверяет, относится ли новость к финансовым рынкам:
- **Ключевые слова**: bitcoin, ethereum, stock, trading, market, etc.
- **Минимум 2 ключевых слова** для прохождения фильтра
- **Исключения**: спорт, развлечения, общие новости

### 3.4 Категоризация
Автоматическое определение категории новости:
- **Crypto**: bitcoin, ethereum, defi, nft
- **Stocks**: nasdaq, nyse, earnings, ipo
- **Whales**: large transaction, institutional
- **AI**: artificial intelligence, machine learning
- **Politics**: regulation, government, policy

---

## 🤖 Этап 4: AI Анализ (Самый Важный)

### 4.1 Подготовка к Анализу
Перед отправкой в AI система:
- **Обрезает текст** до 4000 символов (лимит API)
- **Получает рыночный контекст** (цена Bitcoin, индекс страха/жадности)
- **Ищет исторические аналогии** в базе знаний
- **Формирует расширенный промпт** с контекстом

### 4.2 DeepSeek AI Обработка
Отправляется запрос к DeepSeek API с детальным промптом:

**Что AI анализирует:**
- **Sentiment** (настроение): positive/negative/neutral
- **Market Impact** (влияние на рынок): high/medium/low
- **Price Impact** (влияние на цену): immediate/short-term/long-term
- **Affected Assets** (затронутые активы): BTC, ETH, stocks
- **Risk Assessment** (оценка рисков): high/medium/low
- **Key Points** (ключевые моменты): массив важных фактов
- **Tags** (теги): релевантные метки

**Что AI создает:**
- **Улучшенный заголовок** (более привлекательный)
- **Краткое резюме** (2-3 предложения)
- **Переписанный контент** (более читаемый)
- **Детальное обоснование** анализа

### 4.3 Обработка Результата
AI возвращает JSON, который система:
- **Валидирует** на корректность
- **Нормализует** значения (например, sentiment score 0-100)
- **Применяет строгие пороги** для классификации
- **Добавляет метаданные** (время анализа, версия модели)

### 4.4 Fallback Механизм
Если AI недоступен, система использует:
- **Простой анализ по ключевым словам**
- **Базовую категоризацию**
- **Нейтральный sentiment**
- **Пометку "fallback analysis"**

---

## 💾 Этап 5: Кэширование и Хранение

### 5.1 Многоуровневое Кэширование
- **Memory Cache** (NodeCache): результаты AI анализа на 30 минут
- **File Cache** (JSON файлы): последние 100 новостей
- **Backup System**: ежедневные backup с ротацией

### 5.2 Структура Данных
Каждая новость сохраняется как объект:
```
{
  id: "уникальный_идентификатор",
  title: "AI-улучшенный заголовок",
  aiGeneratedTitle: "оригинальный заголовок", 
  description: "описание",
  content: "полный текст",
  summary: "AI резюме",
  publishedAt: "2024-01-01T12:00:00Z",
  source: "CoinDesk",
  url: "https://...",
  sentiment: "positive",
  sentimentData: {
    score: 75,
    confidence: 85,
    reasoning: "обоснование AI",
    marketImpact: "high",
    timeframe: "short-term"
  },
  category: "crypto",
  tags: ["bitcoin", "price", "bullish"],
  keyPoints: ["важный факт 1", "важный факт 2"],
  fetchedAt: "время получения",
  cachedAt: "время кэширования"
}
```

---

## 📡 Этап 6: Real-time Доставка

### 6.1 Server-Sent Events (SSE)
Как только новость проанализирована:
- **Добавляется в кэш** (newsFeed массив)
- **Отправляется всем подключенным клиентам** через SSE
- **Логируется статистика** отправки

### 6.2 Типы Событий
- **"connected"**: подтверждение подключения
- **"news-added"**: новая проанализированная новость
- **"heartbeat"**: проверка соединения каждые 30 секунд

### 6.3 Обработка Отключений
- **Автоматическое переподключение** на клиенте
- **Буферизация событий** при временных разрывах
- **Graceful degradation** при недоступности сервера

---

## 🔄 Жизненный Цикл Новости

### Временные Характеристики:
1. **Публикация на источнике** → 0 минут
2. **Обнаружение системой** → 0-15 минут (зависит от расписания)
3. **Парсинг и фильтрация** → +10-30 секунд
4. **AI анализ** → +30-90 секунд
5. **Кэширование** → +1-2 секунды
6. **Доставка клиенту** → +1-5 секунд

**Общее время: 2-4 минуты** от публикации до пользователя

### Пример Полного Цикла:
```
09:00:00 - CoinDesk публикует статью о Bitcoin
09:05:00 - Наш планировщик запускает сбор новостей
09:05:10 - Система находит новую статью в RSS
09:05:15 - Парсер извлекает полный текст
09:05:20 - Проходит фильтрацию (500+ слов, финансовая тематика)
09:05:25 - Создается fingerprint, проверяется дедупликация
09:05:30 - Отправляется в очередь AI анализа
09:06:30 - DeepSeek возвращает анализ (sentiment: positive, impact: high)
09:06:35 - Результат валидируется и нормализуется
09:06:40 - Сохраняется в кэш и файловую систему
09:06:45 - Отправляется всем подключенным клиентам через SSE
09:06:50 - Пользователи видят новость в приложении
```

---

## 🛡️ Обработка Ошибок

### На каждом этапе предусмотрены fallback механизмы:

**Сбор данных:**
- Если источник недоступен → пропускаем, логируем
- Если превышен rate limit → откладываем на следующий цикл

**Парсинг:**
- Если HTML не парсится → используем только RSS данные
- Если нет полного текста → используем описание

**AI анализ:**
- Если DeepSeek недоступен → простой keyword-based анализ
- Если JSON некорректный → создаем базовый анализ
- Если превышен лимит → ждем и повторяем

**Доставка:**
- Если SSE соединение разорвано → клиент переподключается
- Если клиент недоступен → удаляем из списка

---

## 📊 Мониторинг и Метрики

### Система отслеживает:
- **Количество обработанных новостей** за день/час
- **Время обработки** каждого этапа
- **Успешность AI анализа** (% успешных запросов)
- **Качество источников** (% релевантных новостей)
- **SSE соединения** (количество подключенных клиентов)
- **Ошибки и их частота**

### Логирование:
Каждое действие логируется с временными метками и контекстом для отладки и оптимизации системы.

---

Эта архитектура обеспечивает **надежную, масштабируемую и быструю** обработку финансовых новостей с высоким качеством AI-анализа.
