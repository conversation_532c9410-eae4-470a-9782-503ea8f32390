const { fetchCryptoCompareNews } = require('./src/adapters/cryptocompare');
const { fetchNewsApiNews } = require('./src/adapters/newsapi');
const { fetchAllPremiumCryptoNews } = require('./src/adapters/premiumCrypto');
const { fetchAllPremiumFinanceNews } = require('./src/adapters/premiumFinance');

async function testApiSources() {
  console.log('🔍 ТЕСТИРОВАНИЕ API ИСТОЧНИКОВ НОВОСТЕЙ\n');
  
  let totalNews = 0;
  let fullContentNews = 0;
  let shortContentNews = 0;
  
  // 1. CryptoCompare API
  console.log('📊 1. CRYPTOCOMPARE API');
  console.log('=' .repeat(50));
  try {
    const cryptoNews = await fetchCryptoCompareNews();
    console.log(`✅ Получено новостей: ${cryptoNews.length}`);
    
    cryptoNews.slice(0, 3).forEach((news, index) => {
      const contentLength = news.content ? news.content.length : 0;
      const isFullContent = contentLength > 1000;
      
      if (isFullContent) fullContentNews++;
      else shortContentNews++;
      totalNews++;
      
      console.log(`\n   ${index + 1}. "${news.title.slice(0, 60)}..."`);
      console.log(`      📏 Content: ${contentLength} chars`);
      console.log(`      🔗 URL: ${news.url}`);
      console.log(`      📝 Type: ${isFullContent ? '✅ Full' : '⚠️  Short'}`);
      console.log(`      📅 Published: ${news.publishedAt}`);
      console.log(`      🏢 Source: ${news.source}`);
    });
  } catch (error) {
    console.log(`❌ Ошибка: ${error.message}`);
  }
  
  // 2. NewsAPI
  console.log('\n\n📊 2. NEWSAPI (BUSINESS)');
  console.log('=' .repeat(50));
  try {
    const newsApiNews = await fetchNewsApiNews({ category: 'business', pageSize: 10 });
    console.log(`✅ Получено новостей: ${newsApiNews.length}`);
    
    newsApiNews.slice(0, 3).forEach((news, index) => {
      const contentLength = news.content ? news.content.length : 0;
      const isFullContent = contentLength > 1000;
      
      if (isFullContent) fullContentNews++;
      else shortContentNews++;
      totalNews++;
      
      console.log(`\n   ${index + 1}. "${news.title.slice(0, 60)}..."`);
      console.log(`      📏 Content: ${contentLength} chars`);
      console.log(`      🔗 URL: ${news.url}`);
      console.log(`      📝 Type: ${isFullContent ? '✅ Full' : '⚠️  Short'}`);
      console.log(`      📅 Published: ${news.publishedAt}`);
      console.log(`      🏢 Source: ${news.source}`);
    });
  } catch (error) {
    console.log(`❌ Ошибка: ${error.message}`);
  }
  
  // 3. Premium Crypto Sources
  console.log('\n\n📊 3. PREMIUM CRYPTO SOURCES');
  console.log('=' .repeat(50));
  try {
    const premiumCryptoNews = await fetchAllPremiumCryptoNews();
    console.log(`✅ Получено новостей: ${premiumCryptoNews.length}`);
    
    premiumCryptoNews.slice(0, 3).forEach((news, index) => {
      const contentLength = news.content ? news.content.length : 0;
      const isFullContent = contentLength > 1000;
      
      if (isFullContent) fullContentNews++;
      else shortContentNews++;
      totalNews++;
      
      console.log(`\n   ${index + 1}. "${news.title.slice(0, 60)}..."`);
      console.log(`      📏 Content: ${contentLength} chars`);
      console.log(`      🔗 URL: ${news.url}`);
      console.log(`      📝 Type: ${isFullContent ? '✅ Full' : '⚠️  Short'}`);
      console.log(`      📅 Published: ${news.publishedAt}`);
      console.log(`      🏢 Source: ${news.source}`);
    });
  } catch (error) {
    console.log(`❌ Ошибка: ${error.message}`);
  }
  
  // 4. Premium Finance Sources
  console.log('\n\n📊 4. PREMIUM FINANCE SOURCES');
  console.log('=' .repeat(50));
  try {
    const premiumFinanceNews = await fetchAllPremiumFinanceNews();
    console.log(`✅ Получено новостей: ${premiumFinanceNews.length}`);
    
    premiumFinanceNews.slice(0, 3).forEach((news, index) => {
      const contentLength = news.content ? news.content.length : 0;
      const isFullContent = contentLength > 1000;
      
      if (isFullContent) fullContentNews++;
      else shortContentNews++;
      totalNews++;
      
      console.log(`\n   ${index + 1}. "${news.title.slice(0, 60)}..."`);
      console.log(`      📏 Content: ${contentLength} chars`);
      console.log(`      🔗 URL: ${news.url}`);
      console.log(`      📝 Type: ${isFullContent ? '✅ Full' : '⚠️  Short'}`);
      console.log(`      📅 Published: ${news.publishedAt}`);
      console.log(`      🏢 Source: ${news.source}`);
    });
  } catch (error) {
    console.log(`❌ Ошибка: ${error.message}`);
  }
  
  // Итоговая статистика
  console.log('\n\n📈 ИТОГОВАЯ СТАТИСТИКА');
  console.log('=' .repeat(50));
  console.log(`📰 Всего новостей протестировано: ${totalNews}`);
  console.log(`✅ С полным контентом (>1000 символов): ${fullContentNews}`);
  console.log(`⚠️  С коротким контентом (<1000 символов): ${shortContentNews}`);
  console.log(`📊 Процент качественного контента: ${totalNews > 0 ? Math.round((fullContentNews / totalNews) * 100) : 0}%`);
  
  if (fullContentNews === 0) {
    console.log('\n🚨 ПРОБЛЕМА: Ни одна новость не содержит полного контента!');
    console.log('💡 Возможные причины:');
    console.log('   - API ключи не настроены');
    console.log('   - Проблемы с извлечением контента');
    console.log('   - Блокировка запросов сайтами');
  }
}

testApiSources().catch(console.error);
