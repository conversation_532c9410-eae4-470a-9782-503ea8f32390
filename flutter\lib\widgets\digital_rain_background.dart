import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';

/// Animated background that mimics a subtle "digital rain" inspired by the
/// Matrix but with crypto-flavoured symbols. Columns of characters fall from
/// the top, gently fading out near the bottom. The animation is intentionally
/// soft and low-blur so it does not distract from reading.
class DigitalRainBackground extends StatefulWidget {
  const DigitalRainBackground({Key? key}) : super(key: key);

  @override
  State<DigitalRainBackground> createState() => _DigitalRainBackgroundState();
}

class _DigitalRainBackgroundState extends State<DigitalRainBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<double> _offsets; // foreground positions
  late List<double> _speeds;  // foreground speeds
  late List<double> _offsetsFar; // background (far) positions
  late List<double> _speedsFar;  // background speeds
  int _columns = 0;
  final int _trailLength = 24;
  final Random _rng = Random();

  // Animation settings
  static const double _minSpeed = 0.3;  // Minimum speed (rows/sec)
  static const double _maxSpeed = 1.8;  // Maximum speed (rows/sec)
  static const double _minSpeedFar = 0.15; // Minimum background speed
  static const double _maxSpeedFar = 1.0;  // Maximum background speed

  static const _cryptoSymbols = [
    '₿', 'Ξ', '◎', '₵', '₮', 'Ɍ', // coins
    '↑', '↓',                      // simple growth symbols
  ];

  // Patterns provided by user (20 entries)
  static const List<String> _patterns = [
    r'0X7#K$J%P',
    r'A9Z&Q#R*M',
    r'1B5@N$L%Y',
    r'C3X#P&J*K',
    r'7D9$R@M%Q',
    r'E2F#Z$L&N',
    r'4G8H$Q%@P',
    r'J5K#M&X*R',
    r'6L9N$R%@Y',
    r'P3Q#H$L&M',
    r'A7B$Z#@K',
    r'C9D#R&X%M',
    r'E5F$Q#@N',
    r'G8H#P&L%J',
    r'J2K$Y#@R',
    r'M6N#Q&X%H',
    r'P4Q$R#@L',
    r'R9S#M&J%K',
    r'T3U$Z#@N',
    r'V7W#X&L%P',
  ];

  List<String> _columnPatterns = []; // pattern assigned per column

  @override
  void initState() {
    super.initState();
    print('🌧️ DigitalRainBackground: initState()');
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1), // 1 second for smooth animation
    )..addListener(_tick)
     ..repeat();

    _offsets = const [];
    _speeds  = const [];
    _offsetsFar = const [];
    _speedsFar = const [];
    _columnPatterns = []; // Инициализируем пустым списком
    print('🌧️ DigitalRainBackground: инициализация завершена');
  }

  void _initColumns(double width) {
    const double charWidth = 14.0;
    final int cols = (width / charWidth).floor();
    if (cols == _columns) return;

    _columns = cols;
    _offsets = List.generate(_columns, (_) => _rng.nextDouble() * -20);
    _speeds  = List.generate(_columns, (_) => _minSpeed + _rng.nextDouble() * (_maxSpeed - _minSpeed));
    _offsetsFar = List.generate(_columns, (_) => _rng.nextDouble() * -20);
    _speedsFar  = List.generate(_columns, (_) => _minSpeedFar + _rng.nextDouble() * (_maxSpeedFar - _minSpeedFar));
    
    // assign pattern per column deterministically for stability
    _columnPatterns = List.generate(
      _columns,
      (i) => _patterns[i % _patterns.length],
    );
  }

  void _tick() {
    if (!mounted) return;
    
    setState(() {
      final double dt = 1/60; // Assuming 60 FPS for smooth animation
      
      for (int i = 0; i < _columns; i++) {
        _offsets[i] += _speeds[i] * dt;
        _offsetsFar[i] += _speedsFar[i] * dt;
        
        // Reset column if it has passed the viewport fully
        if (_offsets[i] > 100) { // Arbitrary large number
          _offsets[i] = -20 - _rng.nextDouble() * 10;
          _speeds[i] = _minSpeed + _rng.nextDouble() * (_maxSpeed - _minSpeed);
        }
        
        if (_offsetsFar[i] > 100) {
          _offsetsFar[i] = -20 - _rng.nextDouble() * 10;
          _speedsFar[i] = _minSpeedFar + _rng.nextDouble() * (_maxSpeedFar - _minSpeedFar);
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        print('🌧️ DigitalRainBackground: build() - width=${constraints.maxWidth}, height=${constraints.maxHeight}');
        _initColumns(constraints.maxWidth);

        // Проверяем, что _columnPatterns инициализирован ПОСЛЕ _initColumns
        if (_columnPatterns.isEmpty || _columns == 0) {
          print('❌ DigitalRainBackground: паттерны не готовы (_columns=$_columns, _columnPatterns.length=${_columnPatterns.length})');
          return Container(color: Colors.red.withOpacity(0.1)); // Красный контейнер для отладки
        }

        print('✅ DigitalRainBackground: отображаем дождь (_columns=$_columns)');

        return Stack(
          children: [
            CustomPaint(
              size: Size.infinite,
              painter: _DigitalRainPainter(
                offsets: _offsetsFar,
                speeds: _speedsFar,
                trailLength: _trailLength,
                cryptoSymbols: _cryptoSymbols,
                columnPatterns: _columnPatterns,
                scale: 0.8,
              ),
            ),
            CustomPaint(
              size: Size.infinite,
              painter: _DigitalRainPainter(
                offsets: _offsets,
                speeds: _speeds,
                trailLength: _trailLength,
                cryptoSymbols: _cryptoSymbols,
                columnPatterns: _columnPatterns,
                scale: 1.0,
              ),
            ),
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 1.3, sigmaY: 1.3),
                child: Container(color: Colors.transparent),
              ),
            ),
          ],
        );
      },
    );
  }
}

class _DigitalRainPainter extends CustomPainter {
  _DigitalRainPainter({
    required this.offsets,
    required this.speeds,
    required this.trailLength,
    required this.cryptoSymbols,
    required this.columnPatterns,
    this.scale = 1.0,
  });

  final List<double> offsets;
  final List<double> speeds;
  final int trailLength;
  final List<String> cryptoSymbols;
  final List<String> columnPatterns;
  final double scale;

  final TextPainter _textPainter = TextPainter(
    textAlign: TextAlign.center,
    textDirection: TextDirection.ltr,
  );

  final TextStyle _baseStyle = const TextStyle(
    fontFamily: 'SourceCodePro',
    fontSize: 14,
    color: Colors.greenAccent,
  );

  static final Map<String, TextPainter> _cache = {};

  @override
  void paint(Canvas canvas, Size size) {
    const double baseCharHeight = 18.0;
    const double baseCharWidth = 14.0;
    final double charHeight = baseCharHeight * scale;
    final double charWidth = baseCharWidth * scale;

    // Draw subtle blackish background with slight transparency
    canvas.drawRect(
      Offset.zero & size,
      Paint()..color = const Color(0xFF0B0C0E),
    );

    final int columns = offsets.length;

    for (int col = 0; col < columns; col++) {
      final double x = col * charWidth;
      final double offset = offsets[col];
      final int headRow = offset.floor();

      for (int t = 0; t < trailLength; t++) {
        final int row = headRow - t;
        final double y = row * charHeight;
        if (y < -charHeight || y > size.height + charHeight) continue;

        // Deterministic selection to keep pattern stable and lightweight
        bool isHead = (t == 0);
        String char;
        if (isHead && ((headRow + col) % 12 == 0)) {
          char = cryptoSymbols[(headRow + col) % cryptoSymbols.length];
        } else {
          final String pattern = columnPatterns[col];
          final int idx = (row.abs()) % pattern.length;
          char = pattern[idx];
        }

        // Smooth opacity transition
        final double opacity = (1 - t / trailLength) * 0.6;
        final int quant = (opacity * 10).round();
        final String key = '$char-$quant-$scale';

        TextPainter tp = _cache[key] ??= (
          () {
            final painter = TextPainter(
              textAlign: TextAlign.center,
              textDirection: TextDirection.ltr,
              text: TextSpan(
                text: char,
                style: _baseStyle.copyWith(
                  color: _baseStyle.color!.withOpacity(quant / 10),
                ),
              ),
            )..layout(minWidth: charWidth, maxWidth: charWidth);
            return painter;
          }()
        );

        // Apply smooth vertical position interpolation
        final double interpolatedY = y + (offset - headRow) * charHeight;
        tp.paint(canvas, Offset(x, interpolatedY));
      }
    }
  }

  @override
  bool shouldRepaint(covariant _DigitalRainPainter oldDelegate) => true;
}
