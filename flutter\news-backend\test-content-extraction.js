// Тестовый скрипт для проверки извлечения полного контента
const { fetchDecryptNews, fetchCoinDeskNews } = require('./src/adapters/premiumCrypto');

async function testContentExtraction() {
  console.log('🔧 Testing Full Content Extraction\n');
  console.log('=' .repeat(80));
  console.log('🎯 Testing:');
  console.log('   • Decrypt.co full content extraction');
  console.log('   • CoinDesk full content extraction');
  console.log('   • Content length validation');
  console.log('   • Readability parsing');
  console.log('=' .repeat(80));

  try {
    console.log('\n📰 PHASE 1: Testing Decrypt.co Content Extraction');
    console.log('─'.repeat(60));
    
    const startTime = Date.now();
    const decryptNews = await fetchDecryptNews();
    const endTime = Date.now();
    
    console.log(`\n⏱️  Decrypt extraction completed in ${endTime - startTime}ms`);
    console.log(`📊 Total news extracted: ${decryptNews.length}`);
    
    if (decryptNews.length > 0) {
      console.log('\n📰 Decrypt News Analysis:');
      
      let fullContentCount = 0;
      let shortContentCount = 0;
      let totalContentLength = 0;
      
      decryptNews.slice(0, 5).forEach((news, index) => {
        const contentLength = news.content ? news.content.length : 0;
        const isFullContent = contentLength > 1000;
        
        if (isFullContent) fullContentCount++;
        else shortContentCount++;
        
        totalContentLength += contentLength;
        
        console.log(`\n   ${index + 1}. 📰 "${news.title.slice(0, 60)}..."`);
        console.log(`      📏 Content length: ${contentLength} chars`);
        console.log(`      🔗 URL: ${news.url}`);
        console.log(`      📝 Content type: ${isFullContent ? '✅ Full content' : '⚠️  Short content'}`);
        console.log(`      👤 Author: ${news.author || 'N/A'}`);
        console.log(`      📄 Word count: ${news.wordCount || 'N/A'}`);
        
        if (news.content) {
          console.log(`      📖 Preview: "${news.content.slice(0, 150)}..."`);
        }
      });
      
      const avgContentLength = totalContentLength / decryptNews.length;
      console.log(`\n📊 Decrypt Content Statistics:`);
      console.log(`   • Full content articles: ${fullContentCount}/${decryptNews.length}`);
      console.log(`   • Short content articles: ${shortContentCount}/${decryptNews.length}`);
      console.log(`   • Average content length: ${Math.round(avgContentLength)} chars`);
      console.log(`   • Success rate: ${((fullContentCount / decryptNews.length) * 100).toFixed(1)}%`);
    }

    console.log('\n📰 PHASE 2: Testing CoinDesk Content Extraction');
    console.log('─'.repeat(60));
    
    const coinDeskStartTime = Date.now();
    const coinDeskNews = await fetchCoinDeskNews();
    const coinDeskEndTime = Date.now();
    
    console.log(`\n⏱️  CoinDesk extraction completed in ${coinDeskEndTime - coinDeskStartTime}ms`);
    console.log(`📊 Total news extracted: ${coinDeskNews.length}`);
    
    if (coinDeskNews.length > 0) {
      console.log('\n📰 CoinDesk News Analysis:');
      
      let fullContentCount = 0;
      let shortContentCount = 0;
      let totalContentLength = 0;
      
      coinDeskNews.slice(0, 3).forEach((news, index) => {
        const contentLength = news.content ? news.content.length : 0;
        const isFullContent = contentLength > 1000;
        
        if (isFullContent) fullContentCount++;
        else shortContentCount++;
        
        totalContentLength += contentLength;
        
        console.log(`\n   ${index + 1}. 📰 "${news.title.slice(0, 60)}..."`);
        console.log(`      📏 Content length: ${contentLength} chars`);
        console.log(`      🔗 URL: ${news.url}`);
        console.log(`      📝 Content type: ${isFullContent ? '✅ Full content' : '⚠️  Short content'}`);
        console.log(`      👤 Author: ${news.author || 'N/A'}`);
        console.log(`      📄 Word count: ${news.wordCount || 'N/A'}`);
        
        if (news.content) {
          console.log(`      📖 Preview: "${news.content.slice(0, 150)}..."`);
        }
      });
      
      const avgContentLength = totalContentLength / coinDeskNews.length;
      console.log(`\n📊 CoinDesk Content Statistics:`);
      console.log(`   • Full content articles: ${fullContentCount}/${coinDeskNews.length}`);
      console.log(`   • Short content articles: ${shortContentCount}/${coinDeskNews.length}`);
      console.log(`   • Average content length: ${Math.round(avgContentLength)} chars`);
      console.log(`   • Success rate: ${((fullContentCount / coinDeskNews.length) * 100).toFixed(1)}%`);
    }

    console.log('\n📊 PHASE 3: Testing Specific URL');
    console.log('─'.repeat(60));
    
    // Тестируем конкретную статью Decrypt
    const testUrl = 'https://decrypt.co/325277/this-week-crypto-games-dogecoin-gaming-fifa-rivals';
    console.log(`\n🔍 Testing specific URL: ${testUrl}`);
    
    try {
      const { extractFullContent } = require('./src/adapters/premiumCrypto');
      const specificContent = await extractFullContent(testUrl);
      
      if (specificContent) {
        console.log(`✅ Successfully extracted content:`);
        console.log(`   • Title: "${specificContent.title}"`);
        console.log(`   • Content length: ${specificContent.length} chars`);
        console.log(`   • Author: ${specificContent.byline || 'N/A'}`);
        console.log(`   • Excerpt: "${specificContent.excerpt || 'N/A'}"`);
        console.log(`   • Preview: "${specificContent.content.slice(0, 300)}..."`);
      } else {
        console.log(`❌ Failed to extract content from specific URL`);
      }
    } catch (error) {
      console.log(`❌ Error testing specific URL: ${error.message}`);
    }

    console.log('\n🎉 CONTENT EXTRACTION TEST COMPLETED!');
    console.log('=' .repeat(80));
    console.log('📊 Summary:');
    console.log('   ✅ Full content extraction implemented for Decrypt.co');
    console.log('   ✅ Full content extraction implemented for CoinDesk');
    console.log('   ✅ Readability.js integration for better parsing');
    console.log('   ✅ Fallback mechanisms for failed extractions');
    console.log('   ✅ Detailed logging and error handling');
    
    console.log('\n🎯 Key Improvements:');
    console.log('   • Mozilla Readability for clean content extraction');
    console.log('   • Site-specific CSS selectors as fallbacks');
    console.log('   • Content length validation (minimum 500 chars)');
    console.log('   • Author and metadata extraction');
    console.log('   • Graceful degradation to RSS description');
    console.log('   • Performance monitoring and logging');

  } catch (error) {
    console.error('❌ Content extraction test failed:', error.message);
    console.error(error.stack);
  }
}

// Запускаем тест
if (require.main === module) {
  testContentExtraction().catch(error => {
    console.error('❌ Content extraction test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testContentExtraction
};
