const cron = require('node-cron');
const { startBackgroundParseAndAnalyze } = require('./newsService');

class NewsScheduler {
  constructor() {
    this.isRunning = false;
    this.lastUpdate = null;
    this.updateCount = 0;
    this.errors = [];
    this.maxErrors = 10;
    this.broadcastCallback = null;
    
    // Настройки расписания
    this.schedules = {
      // Каждые 15 минут в рабочие часы (9:00-21:00 UTC)
      frequent: '*/15 9-21 * * *',
      // Каждый час в нерабочие часы
      hourly: '0 22-8 * * *',
      // Каждые 5 минут для премиум источников (только в рабочие часы)
      premium: '*/5 9-21 * * 1-5'
    };
  }

  setBroadcastCallback(callback) {
    this.broadcastCallback = callback;
  }

  start() {
    if (this.isRunning) {
      console.log('[SCHEDULER] Already running');
      return;
    }

    console.log('[SCHEDULER] Starting news scheduler...');
    this.isRunning = true;

    // Основное расписание - каждые 15 минут в рабочие часы
    this.frequentTask = cron.schedule(this.schedules.frequent, async () => {
      await this.executeUpdate('frequent');
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    // Ночное расписание - каждый час
    this.hourlyTask = cron.schedule(this.schedules.hourly, async () => {
      await this.executeUpdate('hourly');
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    // Премиум источники - каждые 5 минут в рабочие дни
    if (process.env.ENABLE_PREMIUM_SOURCES === 'true') {
      this.premiumTask = cron.schedule(this.schedules.premium, async () => {
        await this.executeUpdate('premium');
      }, {
        scheduled: false,
        timezone: 'UTC'
      });
      this.premiumTask.start();
    }

    this.frequentTask.start();
    this.hourlyTask.start();

    // Первое обновление сразу при запуске
    setTimeout(() => {
      this.executeUpdate('startup');
    }, 5000);

    console.log('[SCHEDULER] News scheduler started successfully');
  }

  stop() {
    if (!this.isRunning) {
      console.log('[SCHEDULER] Not running');
      return;
    }

    console.log('[SCHEDULER] Stopping news scheduler...');
    
    if (this.frequentTask) this.frequentTask.stop();
    if (this.hourlyTask) this.hourlyTask.stop();
    if (this.premiumTask) this.premiumTask.stop();

    this.isRunning = false;
    console.log('[SCHEDULER] News scheduler stopped');
  }

  async executeUpdate(type = 'manual') {
    if (!this.isRunning) {
      console.log('[SCHEDULER] Scheduler is stopped, skipping update');
      return;
    }

    const startTime = Date.now();
    console.log(`[SCHEDULER] Starting ${type} news update...`);

    try {
      const result = await startBackgroundParseAndAnalyze(this.broadcastCallback);
      
      this.lastUpdate = new Date();
      this.updateCount++;
      
      const duration = Date.now() - startTime;
      console.log(`[SCHEDULER] ${type} update completed in ${duration}ms`);
      console.log(`[SCHEDULER] Total updates: ${this.updateCount}`);
      
      // Очищаем старые ошибки при успешном обновлении
      if (this.errors.length > 0) {
        this.errors = this.errors.slice(-3); // Оставляем только последние 3 ошибки
      }

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[SCHEDULER] ${type} update failed after ${duration}ms:`, error.message);
      
      this.errors.push({
        type,
        error: error.message,
        timestamp: new Date(),
        duration
      });

      // Ограничиваем количество сохраняемых ошибок
      if (this.errors.length > this.maxErrors) {
        this.errors = this.errors.slice(-this.maxErrors);
      }

      // Если слишком много ошибок подряд, увеличиваем интервал
      const recentErrors = this.errors.filter(e => 
        Date.now() - e.timestamp.getTime() < 30 * 60 * 1000 // За последние 30 минут
      );

      if (recentErrors.length >= 5) {
        console.warn('[SCHEDULER] Too many recent errors, implementing backoff');
        // Можно добавить логику увеличения интервала
      }

      throw error;
    }
  }

  // Принудительное обновление
  async forceUpdate() {
    console.log('[SCHEDULER] Force update requested');
    return await this.executeUpdate('force');
  }

  // Получение статистики
  getStats() {
    const recentErrors = this.errors.filter(e => 
      Date.now() - e.timestamp.getTime() < 60 * 60 * 1000 // За последний час
    );

    return {
      isRunning: this.isRunning,
      lastUpdate: this.lastUpdate,
      updateCount: this.updateCount,
      totalErrors: this.errors.length,
      recentErrors: recentErrors.length,
      uptime: this.lastUpdate ? Date.now() - this.lastUpdate.getTime() : null,
      schedules: this.schedules,
      premiumEnabled: process.env.ENABLE_PREMIUM_SOURCES === 'true'
    };
  }

  // Получение последних ошибок
  getRecentErrors(limit = 5) {
    return this.errors
      .slice(-limit)
      .map(e => ({
        type: e.type,
        error: e.error,
        timestamp: e.timestamp,
        duration: e.duration
      }));
  }

  // Проверка здоровья планировщика
  healthCheck() {
    const stats = this.getStats();
    const now = Date.now();
    
    // Проверяем, было ли обновление в последние 30 минут
    const timeSinceLastUpdate = stats.lastUpdate ? 
      now - stats.lastUpdate.getTime() : Infinity;
    
    const isHealthy = stats.isRunning && 
      timeSinceLastUpdate < 30 * 60 * 1000 && // Менее 30 минут назад
      stats.recentErrors < 3; // Менее 3 ошибок за час

    return {
      healthy: isHealthy,
      issues: [
        !stats.isRunning && 'Scheduler not running',
        timeSinceLastUpdate > 30 * 60 * 1000 && 'No recent updates',
        stats.recentErrors >= 3 && 'Too many recent errors'
      ].filter(Boolean),
      stats
    };
  }
}

// Создаем единственный экземпляр планировщика
const newsScheduler = new NewsScheduler();

module.exports = {
  NewsScheduler,
  newsScheduler
};
