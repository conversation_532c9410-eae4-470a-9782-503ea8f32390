import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/news_item.dart';
import '../widgets/ios_style_filter_panel.dart';
import '../widgets/app_bottom_navigation.dart';

import '../providers/news_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/device_type.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'dart:math';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:finance_ai/models/sentiment_types.dart';
import '../widgets/ultra_gradient_news_card.dart';

import '../utils/modal_utils.dart';
import '../utils/news_filter_utils.dart';
import '../widgets/news_dropdown_menu.dart';
import '../widgets/side_drawer_menu.dart';
import '../widgets/digital_rain_background.dart';

class TypewriterText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;

  const TypewriterText({
    required this.text,
    this.style,
    this.duration = const Duration(milliseconds: 600),
    super.key,
  });

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _charCount;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _charCount = StepTween(begin: 0, end: widget.text.length)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
    _controller.forward();
  }

  @override
  void didUpdateWidget(TypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _controller.reset();
      _charCount = StepTween(begin: 0, end: widget.text.length)
          .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _charCount,
      builder: (context, child) {
        String visibleText = widget.text.substring(0, _charCount.value);
        return Text(visibleText, style: widget.style);
      },
    );
  }
}

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();

  bool isMenuOpen = false;
  bool isMenuHovered = false;
  late AnimationController _drawerController;
  late AnimationController _cardAnimationController;
  late AnimationController _sidebarController;

  // Добавляем состояния для фильтрации
  String? _selectedTag;
  SentimentType? _selectedSentiment;



  @override
  void initState() {
    super.initState();
    _drawerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _cardAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _sidebarController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    // Connect to real-time news stream when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final newsProvider = Provider.of<NewsProvider>(context, listen: false);
      // Подключаемся к real-time потоку новостей
      await newsProvider.connectToNewsStream();
      _cardAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _drawerController.dispose();
    _cardAnimationController.dispose();
    _sidebarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final newsProvider = Provider.of<NewsProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final sentiments = SentimentType.values;

    // Собираем уникальные теги из всех новостей
    final tags = newsProvider.filteredNews
        .expand((item) => item.tags)
        .toSet()
        .toList();

    // Получаем отфильтрованные новости из NewsProvider
    List<NewsItem> filteredNews = newsProvider.filteredNews;



    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth >= 1200;

    return Scaffold(
      backgroundColor: const Color(0xFF0A0B0D),
      body: Stack(
        children: [
          // Цифровой дождь фон
          const Positioned.fill(
            child: DigitalRainBackground(),
          ),

          // Основной контент новостей (занимает всю ширину)
          Positioned.fill(
            child: _buildNewsContent(filteredNews),
          ),

          // Боковая панель для больших экранов (накладывается поверх)
          if (isLargeScreen)
            _buildSideNavigation(),

          // Кнопка фильтров для мобильных устройств
          if (!isLargeScreen) ...[
            Positioned(
              top: 50,
              right: 20,
              child: SafeArea(
                child: _buildMobileFilterButtonContent(),
              ),
            ),
          ],
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 0,
        onTap: (index) {
          switch (index) {
            case 0:
              if (ModalRoute.of(context)?.settings.name != '/news') {
                Navigator.of(context).pushReplacementNamed('/news');
              }
              break;
            case 1:
              if (ModalRoute.of(context)?.settings.name != '/charts') {
                Navigator.of(context).pushReplacementNamed('/charts');
              }
              break;
            case 2:
              if (ModalRoute.of(context)?.settings.name != '/sinusoid') {
                Navigator.of(context).pushReplacementNamed('/sinusoid');
              }
              break;
            case 3:
              if (ModalRoute.of(context)?.settings.name != '/courses') {
                Navigator.of(context).pushReplacementNamed('/courses');
              }
              break;
            case 4:
              if (ModalRoute.of(context)?.settings.name != '/profile') {
                Navigator.of(context).pushReplacementNamed('/profile');
              }
              break;
          }
        },
      ),
    );
  }

  Widget _buildSideNavigation() {
    // Принудительная проверка размера экрана
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 1200) {
      print('🚫 ПРИНУДИТЕЛЬНО скрываем боковую панель: width=$screenWidth');
      return const SizedBox.shrink();
    }

    final newsProvider = Provider.of<NewsProvider>(context);

    return AnimatedBuilder(
      animation: _sidebarController,
      builder: (context, child) {
        final isExpanded = isMenuHovered || _sidebarController.value > 0.5;
        final width = Tween<double>(begin: 80.0, end: 240.0)
            .animate(CurvedAnimation(
              parent: _sidebarController,
              curve: Curves.easeInOutCubic,
            )).value;
        final showText = width > 180;

        return Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          child: MouseRegion(
            onEnter: (_) {
              setState(() => isMenuHovered = true);
              _sidebarController.forward();
            },
            onExit: (_) {
              setState(() => isMenuHovered = false);
              _sidebarController.reverse();
            },
            child: Container(
              width: width,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF1a1a1a).withOpacity(0.95),
                    const Color(0xFF0d1117).withOpacity(0.95),
                  ],
                ),
                border: Border(
                  right: BorderSide(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                boxShadow: isExpanded ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(5, 0),
                  ),
                ] : [],
              ),
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 24),
                    child: Column(
                      children: [
                        // Заголовок
                        Container(
                          height: 60,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            children: [
                              Icon(
                                Icons.article,
                                color: Colors.blue[400],
                                size: 24,
                              ),
                              if (showText) ...[
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'News',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Категории
                        Expanded(
                          child: ListView(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            children: [
                              _buildSideNavItem(
                                icon: Icons.all_inclusive,
                                label: 'All',
                                isSelected: newsProvider.selectedCategory == NewsCategory.all,
                                onTap: () => newsProvider.setCategory(NewsCategory.all),
                                showText: showText,
                              ),
                              ...NewsCategory.values.where((cat) => cat != NewsCategory.all).map((category) =>
                                _buildSideNavItem(
                                  icon: _getCategoryIcon(category),
                                  label: _getCategoryLabel(category),
                                  isSelected: newsProvider.selectedCategory == category,
                                  onTap: () => newsProvider.setCategory(category),
                                  showText: showText,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Индикатор подключения к потоку
                        Consumer<NewsProvider>(
                          builder: (context, newsProvider, child) {
                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: newsProvider.isStreamConnected
                                    ? Colors.green.withOpacity(0.1)
                                    : Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: newsProvider.isStreamConnected
                                      ? Colors.green.withOpacity(0.3)
                                      : Colors.orange.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: newsProvider.isStreamConnected
                                          ? Colors.green
                                          : Colors.orange,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  if (showText) ...[
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        newsProvider.isStreamConnected
                                            ? 'Live Updates'
                                            : 'Connecting...',
                                        style: TextStyle(
                                          color: newsProvider.isStreamConnected
                                              ? Colors.green
                                              : Colors.orange,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSideNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool showText,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue[600]?.withOpacity(0.2) : null,
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? Border.all(color: Colors.blue[400]!, width: 1)
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected ? Colors.blue[400] : Colors.grey[400],
                  size: 20,
                ),
                if (showText) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[300],
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(NewsCategory category) {
    switch (category) {
      case NewsCategory.all:
        return Icons.all_inclusive;
      case NewsCategory.crypto:
        return Icons.currency_bitcoin;
      case NewsCategory.stocks:
        return Icons.trending_up;
      case NewsCategory.whales:
        return Icons.waves;
      case NewsCategory.ai:
        return Icons.smart_toy;
      case NewsCategory.politics:
        return Icons.account_balance;
      default:
        return Icons.article;
    }
  }

  String _getCategoryLabel(NewsCategory category) {
    switch (category) {
      case NewsCategory.all:
        return 'All';
      case NewsCategory.crypto:
        return 'Crypto';
      case NewsCategory.stocks:
        return 'Stocks';
      case NewsCategory.whales:
        return 'Whales';
      case NewsCategory.ai:
        return 'AI';
      case NewsCategory.politics:
        return 'Politics';
      default:
        return category.name;
    }
  }



  Widget _buildNewsContent(List<NewsItem> filteredNews) {
    if (filteredNews.isEmpty) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF0A0B0D).withOpacity(0.3),
              const Color(0xFF1a1a1a).withOpacity(0.5),
              const Color(0xFF0A0B0D).withOpacity(0.3),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.article_outlined,
                size: 64,
                color: Colors.white.withOpacity(0.6),
              ),
              const SizedBox(height: 16),
              Text(
                'Загружаем новости...',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Подключение к серверу...',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Определяем размеры карточек по распределению
    int cardCount = filteredNews.length;
    int largeCount = (cardCount * 0.2).round();
    int compactCount = (cardCount * 0.3).round();
    int tallCount = (cardCount * 0.15).round();
    int standardCount = cardCount - largeCount - compactCount - tallCount;

    List<String> distribution = [];
    distribution.addAll(List.filled(largeCount, 'large'));
    distribution.addAll(List.filled(standardCount, 'standard'));
    distribution.addAll(List.filled(compactCount, 'compact'));
    distribution.addAll(List.filled(tallCount, 'tall'));
    distribution.shuffle(Random(cardCount));

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          child: MasonryGridView.count(
            padding: const EdgeInsets.all(20),
            crossAxisCount: MediaQuery.of(context).size.width > 1200 ? 3 : 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            itemCount: filteredNews.length,
            itemBuilder: (context, index) {
              final news = filteredNews[index];
              final size = distribution[index % distribution.length];
              
              CardSize cardSize;
              switch (size) {
                case 'large':
                  cardSize = CardSize.large;
                  break;
                case 'compact':
                  cardSize = CardSize.compact;
                  break;
                case 'tall':
                  cardSize = CardSize.tall;
                  break;
                default:
                  cardSize = CardSize.standard;
              }

              return Container(
                key: ValueKey('news-clean-${news.id}-${index}'), // Уникальный ключ
                child: _buildGlassNewsCard(news, cardSize, index),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildGlassNewsCard(NewsItem news, CardSize size, int index) {
    return UltraGradientNewsCard(
      key: ValueKey('card-clean-${news.id}'), // Уникальный ключ для карточки
      newsItem: news,
      size: size,
      onTap: () => showNewsDetailModal(
        context: context,
        newsItem: news,
      ),
    );
  }
  Widget _buildMobileFilterButtonContent() {
    return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF2a2a2a).withOpacity(0.95),
                const Color(0xFF1d1d1d).withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
              BoxShadow(
                color: Colors.blue.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(25),
              onTap: () {
                _showMobileFilterModal();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.tune_rounded,
                      color: Colors.white.withOpacity(0.9),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Filters',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (newsProvider.selectedCategory != NewsCategory.all) ...[
                      const SizedBox(width: 8),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
  }

  void _showMobileFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildAppleStyleFilterModal(),
    );
  }

  Widget _buildAppleStyleFilterModal() {
    final newsProvider = Provider.of<NewsProvider>(context);

    return Container(
      height: 320,
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: Column(
          children: [
            // Индикатор перетаскивания
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 36,
              height: 5,
              decoration: BoxDecoration(
                color: const Color(0xFF48484A),
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),

            const SizedBox(height: 20),

            // Список категорий
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // All категория
                    _buildMobileCategoryItem(
                      icon: Icons.all_inclusive,
                      label: 'All',
                      isSelected: newsProvider.selectedCategory == NewsCategory.all,
                      onTap: () => newsProvider.setCategory(NewsCategory.all),
                    ),

                    // Остальные категории
                    ...NewsCategory.values.where((cat) => cat != NewsCategory.all).map((category) =>
                      _buildMobileCategoryItem(
                        icon: _getCategoryIcon(category),
                        label: _getCategoryLabel(category),
                        isSelected: newsProvider.selectedCategory == category,
                        onTap: () => newsProvider.setCategory(category),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Кнопка закрытия
                    Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        color: const Color(0xFF007AFF),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.pop(context),
                          child: const Center(
                            child: Text(
                              'Done',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileCategoryItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected ? const Color(0xFF007AFF) : const Color(0xFF2C2C2E),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (isSelected)
                  const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }



}