import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/news_item.dart';
import '../widgets/ios_style_filter_panel.dart';
import '../widgets/app_bottom_navigation.dart';
import '../widgets/news_categories_dock.dart';
import '../providers/news_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/device_type.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'dart:math';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:finance_ai/models/sentiment_types.dart';
import '../widgets/ultra_gradient_news_card.dart';

import '../utils/modal_utils.dart';
import '../utils/news_filter_utils.dart';
import '../widgets/news_dropdown_menu.dart';
import '../widgets/side_drawer_menu.dart';
import '../widgets/digital_rain_background.dart';

class TypewriterText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;

  const TypewriterText({
    required this.text,
    this.style,
    this.duration = const Duration(milliseconds: 600),
    super.key,
  });

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _charCount;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _charCount = StepTween(begin: 0, end: widget.text.length)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
    _controller.forward();
  }

  @override
  void didUpdateWidget(TypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _controller.reset();
      _charCount = StepTween(begin: 0, end: widget.text.length)
          .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _charCount,
      builder: (context, child) {
        String visibleText = widget.text.substring(0, _charCount.value);
        return Text(visibleText, style: widget.style);
      },
    );
  }
}

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  int selectedCategory = 0;
  bool isMenuOpen = false;
  bool isMenuHovered = false;
  late AnimationController _drawerController;
  late AnimationController _cardAnimationController;
  late AnimationController _sidebarController;

  // Добавляем состояния для фильтрации
  String? _selectedTag;
  SentimentType? _selectedSentiment;

  final List<NewsCategoryModel> _categories = [
    NewsCategoryModel(
      id: 0,
      title: 'All News',
      icon: Icons.public_rounded,
      gradient: const LinearGradient(
        colors: [Color(0xFF2C2C2E), Color(0xFF1C1C1E)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    NewsCategoryModel(
      id: 1,
      title: 'Cryptocurrencies',
      icon: Icons.currency_bitcoin_rounded,
      gradient: const LinearGradient(
        colors: [Color(0xFF3A3A3C), Color(0xFF2C2C2E)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    NewsCategoryModel(
      id: 2,
      title: 'Stocks',
      icon: Icons.trending_up_rounded,
      gradient: const LinearGradient(
        colors: [Color(0xFF48484A), Color(0xFF3A3A3C)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    NewsCategoryModel(
      id: 3,
      title: 'Whales',
      icon: Icons.waves_rounded,
      gradient: const LinearGradient(
        colors: [Color(0xFF636366), Color(0xFF48484A)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _drawerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _cardAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _sidebarController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    // Connect to real-time news stream when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final newsProvider = Provider.of<NewsProvider>(context, listen: false);
      // Подключаемся к real-time потоку новостей
      await newsProvider.connectToNewsStream();
      _cardAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _drawerController.dispose();
    _cardAnimationController.dispose();
    _sidebarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final newsProvider = Provider.of<NewsProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final sentiments = SentimentType.values;

    // Собираем уникальные теги из всех новостей
    final tags = newsProvider.filteredNews
        .expand((item) => item.tags)
        .toSet()
        .toList();

    // Фильтрация новостей по выбранной категории
    List<NewsItem> filteredNews = newsProvider.filteredNews;
    switch (selectedCategory) {
      case 1: // Cryptocurrencies
        filteredNews = filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'crypto' ||
          n.category == NewsCategory.crypto ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('crypto')) == true
        ).toList();
        break;
      case 2: // Stocks
        filteredNews = filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'stocks' ||
          n.category == NewsCategory.stocks ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('stocks')) == true
        ).toList();
        break;
      case 3: // Whales
        filteredNews = filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'whales' ||
          n.category == NewsCategory.whales ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('whale')) == true
        ).toList();
        break;
      default: // All News
        break;
    }

    print('🔍 Build: filteredNews.length = ${filteredNews.length}');
    print('🔍 Build: selectedCategory = $selectedCategory');

    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth >= 1200;

    return Scaffold(
      backgroundColor: const Color(0xFF0A0B0D),
      body: Stack(
        children: [
          // Цифровой дождь фон
          const Positioned.fill(
            child: DigitalRainBackground(),
          ),

          // Основной контент
          Positioned.fill(
            child: Row(
              children: [
                // Боковая панель для больших экранов
                if (isLargeScreen)
                  _buildCompactSideNavigation(),

                // Основной контент новостей
                Expanded(
                  child: _buildNewsContent(filteredNews),
                ),
              ],
            ),
          ),

          // Кнопка фильтров для мобильных устройств
          if (!isLargeScreen)
            Positioned(
              top: 50,
              right: 20,
              child: SafeArea(
                child: _buildMobileFilterButtonContent(),
              ),
            ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 0,
        onTap: (index) {
          switch (index) {
            case 0:
              if (ModalRoute.of(context)?.settings.name != '/news') {
                Navigator.of(context).pushReplacementNamed('/news');
              }
              break;
            case 1:
              if (ModalRoute.of(context)?.settings.name != '/charts') {
                Navigator.of(context).pushReplacementNamed('/charts');
              }
              break;
            case 2:
              if (ModalRoute.of(context)?.settings.name != '/sinusoid') {
                Navigator.of(context).pushReplacementNamed('/sinusoid');
              }
              break;
            case 3:
              if (ModalRoute.of(context)?.settings.name != '/courses') {
                Navigator.of(context).pushReplacementNamed('/courses');
              }
              break;
            case 4:
              if (ModalRoute.of(context)?.settings.name != '/profile') {
                Navigator.of(context).pushReplacementNamed('/profile');
              }
              break;
          }
        },
      ),
    );
  }

  Widget _buildCompactSideNavigation() {
    // Принудительная проверка размера экрана
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 1200) {
      print('🚫 ПРИНУДИТЕЛЬНО скрываем боковую панель: width=$screenWidth');
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _sidebarController,
      builder: (context, child) {
        final newsProvider = Provider.of<NewsProvider>(context);
        final isExpanded = isMenuHovered || _sidebarController.value > 0.5;
        final width = Tween<double>(begin: 80.0, end: 240.0)
            .animate(CurvedAnimation(
              parent: _sidebarController,
              curve: Curves.easeInOutCubic,
            )).value;
        final adjustedWidth = width + 4.0;

        final showText = adjustedWidth > 180;
        
        return Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          width: adjustedWidth,
          child: ClipRect(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: adjustedWidth,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF1a1a1a).withOpacity(0.95),
                    const Color(0xFF0d1117).withOpacity(0.95),
                  ],
                ),
                border: Border(
                  right: BorderSide(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                boxShadow: isExpanded ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(5, 0),
                  ),
                ] : [],
              ),
              child: MouseRegion(
                onEnter: (_) {
                  setState(() => isMenuHovered = true);
                  _sidebarController.forward();
                },
                onExit: (_) {
                  setState(() => isMenuHovered = false);
                  _sidebarController.reverse();
                },
                child: ClipRRect(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 24),
                      child: Column(
                        children: [
                          const SizedBox(height: 40),
                          
                          // Категории
                          ...List.generate(_categories.length, (index) {
                            return _buildCompactNavItem(
                              _categories[index],
                              index == selectedCategory,
                              isExpanded,
                              showText,
                              adjustedWidth,
                              newsProvider,
                            );
                          }),

                          const Spacer(),

                          // Индикатор подключения к потоку
                          Consumer<NewsProvider>(
                            builder: (context, newsProvider, child) {
                              return Container(
                                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: newsProvider.isStreamConnected
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.orange.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: newsProvider.isStreamConnected
                                        ? Colors.green.withOpacity(0.3)
                                        : Colors.orange.withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 8,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        color: newsProvider.isStreamConnected
                                            ? Colors.green
                                            : Colors.orange,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    if (isExpanded && showText) ...[
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          newsProvider.isStreamConnected
                                              ? 'Live Updates'
                                              : 'Connecting...',
                                          style: TextStyle(
                                            color: newsProvider.isStreamConnected
                                                ? Colors.green
                                                : Colors.orange,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactNavItem(NewsCategoryModel category, bool isSelected, bool isExpanded, bool showText, double panelWidth, NewsProvider newsProvider) {
    // Подсчитываем количество новостей для данной категории
    int newsCount = 0;
    switch (category.id) {
      case 0: // All News
        newsCount = newsProvider.filteredNews.length;
        break;
      case 1: // Cryptocurrencies
        newsCount = newsProvider.filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'crypto' ||
          n.category == NewsCategory.crypto ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('crypto')) == true
        ).length;
        break;
      case 2: // Stocks
        newsCount = newsProvider.filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'stocks' ||
          n.category == NewsCategory.stocks ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('stocks')) == true
        ).length;
        break;
      case 3: // Whales
        newsCount = newsProvider.filteredNews.where((n) =>
          n.newsCategory?.toLowerCase() == 'whales' ||
          n.category == NewsCategory.whales ||
          n.classificationTags?.any((tag) => tag.toLowerCase().contains('whale')) == true
        ).length;
        break;
    }
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 4,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            setState(() {
              selectedCategory = category.id;
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 48,
            decoration: BoxDecoration(
              gradient: isSelected ? category.gradient : null,
              color: isSelected ? null : Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected 
                    ? Colors.white.withOpacity(0.3)
                    : Colors.white.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: isSelected ? [
                BoxShadow(
                  color: category.gradient.colors.first.withOpacity(0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ] : [],
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  alignment: Alignment.center,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      category.id == 3
                        ? SvgPicture.asset(
                            'logo/news/whale-big-animal-giant-svgrepo-com.svg',
                            width: 22,
                            height: 22,
                            colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                          )
                        : Icon(
                            category.icon,
                            color: Colors.white,
                            size: 22,
                          ),
                      // Показываем количество новостей в свернутом состоянии
                      if (!isExpanded && newsCount > 0)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.white.withOpacity(0.9)
                                  : Colors.red.withOpacity(0.8),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.black.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                newsCount > 99 ? '99+' : '$newsCount',
                                style: TextStyle(
                                  color: isSelected ? Colors.black : Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                if (isExpanded && showText) ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: TypewriterText(
                      key: ValueKey(panelWidth),
                      text: category.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                      duration: const Duration(milliseconds: 600),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Отображение количества новостей
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.white.withOpacity(0.2)
                          : Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Colors.white.withOpacity(0.4)
                            : Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Colors.white.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ] : [],
                    ),
                    child: Text(
                      '$newsCount',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (isSelected) ...[
                    const SizedBox(width: 8),
                    Container(
                      width: 6,
                      height: 6,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                  const SizedBox(width: 8),
                ],
              ],
            ),
          ),
        ),
      ),
    ).animate(target: isSelected ? 1 : 0)
      .scale(begin: const Offset(1, 1), end: const Offset(1.02, 1.02), duration: 200.ms);
  }

  Widget _buildNewsContent(List<NewsItem> filteredNews) {
    print('🔍 _buildNewsContent: filteredNews.length = ${filteredNews.length}');

    if (filteredNews.isEmpty) {
      print('❌ Новостей не найдено, показываем заглушку');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.article_outlined,
              size: 64,
              color: Colors.white.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Новостей не найдено',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    print('✅ Отображаем ${filteredNews.length} новостей');

    // Определяем размеры карточек по распределению
    int cardCount = filteredNews.length;
    int largeCount = (cardCount * 0.2).round();
    int compactCount = (cardCount * 0.3).round();
    int tallCount = (cardCount * 0.15).round();
    int standardCount = cardCount - largeCount - compactCount - tallCount;

    List<String> distribution = [];
    distribution.addAll(List.filled(largeCount, 'large'));
    distribution.addAll(List.filled(standardCount, 'standard'));
    distribution.addAll(List.filled(compactCount, 'compact'));
    distribution.addAll(List.filled(tallCount, 'tall'));
    distribution.shuffle(Random(cardCount));

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          child: MasonryGridView.count(
            padding: const EdgeInsets.all(20),
            crossAxisCount: MediaQuery.of(context).size.width > 1200 ? 3 : 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            itemCount: filteredNews.length,
            itemBuilder: (context, index) {
              final news = filteredNews[index];
              final size = distribution[index % distribution.length];
              
              CardSize cardSize;
              switch (size) {
                case 'large':
                  cardSize = CardSize.large;
                  break;
                case 'compact':
                  cardSize = CardSize.compact;
                  break;
                case 'tall':
                  cardSize = CardSize.tall;
                  break;
                default:
                  cardSize = CardSize.standard;
              }

              return Container(
                key: ValueKey('news-clean-${news.id}-${index}'), // Уникальный ключ
                child: _buildGlassNewsCard(news, cardSize, index),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildGlassNewsCard(NewsItem news, CardSize size, int index) {
    return UltraGradientNewsCard(
      key: ValueKey('card-clean-${news.id}'), // Уникальный ключ для карточки
      newsItem: news,
      size: size,
      onTap: () => showNewsDetailModal(
        context: context,
        newsItem: news,
      ),
    );
  }
  Widget _buildMobileFilterButtonContent() {
    return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1a1a1a).withOpacity(0.9),
                const Color(0xFF0d1117).withOpacity(0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(25),
              onTap: () {
                _showMobileFilterModal();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.tune_rounded,
                      color: Colors.white.withOpacity(0.9),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Filters',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (selectedCategory != 0) ...[
                      const SizedBox(width: 8),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
  }

  void _showMobileFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildMobileFilterModal(),
    );
  }

  Widget _buildMobileFilterModal() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1a1a1a).withOpacity(0.98),
            const Color(0xFF0d1117).withOpacity(0.98),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        child: Column(
          children: [
            // Заголовок модального окна
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Индикатор перетаскивания
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Заголовок
                  Row(
                    children: [
                      Icon(
                        Icons.tune_rounded,
                        color: Colors.white.withOpacity(0.9),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'News Filters',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close_rounded,
                          color: Colors.white.withOpacity(0.7),
                          size: 24,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Контент фильтров
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Категории новостей
                    Text(
                      'Categories',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Список категорий
                    ...List.generate(_categories.length, (index) {
                      return _buildMobileFilterCategoryItem(
                        _categories[index],
                        index == selectedCategory,
                        index,
                      );
                    }),

                    const SizedBox(height: 30),

                    // Кнопки действий
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () {
                                  setState(() {
                                    selectedCategory = 0; // Reset to "All News"
                                  });
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  child: Text(
                                    'Reset',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color(0xFF007bff),
                                  const Color(0xFF0056b3),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () => Navigator.pop(context),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  child: const Text(
                                    'Apply',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileFilterCategoryItem(NewsCategoryModel category, bool isSelected, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: isSelected
            ? LinearGradient(
                colors: [
                  const Color(0xFF007bff).withOpacity(0.3),
                  const Color(0xFF0056b3).withOpacity(0.3),
                ],
              )
            : null,
        color: isSelected
            ? null
            : Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
              ? const Color(0xFF007bff).withOpacity(0.5)
              : Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            setState(() {
              selectedCategory = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Иконка категории
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    gradient: category.gradient,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // Название категории
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.title,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getCategoryDescription(category.title),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),

                // Индикатор выбора
                if (isSelected)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: const Color(0xFF007bff),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                  )
                else
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getCategoryDescription(String categoryTitle) {
    switch (categoryTitle) {
      case 'All News':
        return 'Show all available news';
      case 'Cryptocurrencies':
        return 'Bitcoin, Ethereum, Altcoins';
      case 'Stocks':
        return 'Stock market updates';
      case 'Whales':
        return 'Large transactions & movements';
      default:
        return 'Category news';
    }
  }
}

class NewsCategoryModel {
  final int id;
  final String title;
  final IconData icon;
  final LinearGradient gradient;

  NewsCategoryModel({
    required this.id,
    required this.title,
    required this.icon,
    required this.gradient,
  });
} 