const { analyzeNews } = require('../ai/aiAgent');
const EventEmitter = require('events');

class SequentialNewsAnalyzer extends EventEmitter {
  constructor() {
    super();
    this.isRunning = false;
    this.currentQueue = [];
    this.processedCount = 0;
    this.totalCount = 0;
    this.startTime = null;
    this.analysisInterval = 2 * 60 * 1000; // 2 минуты между анализами
    this.currentAnalysis = null;
    this.stats = {
      analyzed: 0,
      positive: 0,
      negative: 0,
      neutral: 0,
      errors: 0,
      averageTime: 0,
      totalTime: 0
    };
  }

  // Запуск последовательного анализа
  async startSequentialAnalysis(newsList, broadcastCallback = null) {
    if (this.isRunning) {
      console.log('[ANALYZER] ⚠️  Analysis already running');
      return;
    }

    this.isRunning = true;
    this.broadcastCallback = broadcastCallback;
    
    // Сортируем новости от новых к старым (последние 24 часа)
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    const filteredNews = newsList
      .filter(news => {
        const publishedTime = new Date(news.publishedAt).getTime();
        return publishedTime >= oneDayAgo && !news.sentiment; // Только неанализированные за последние 24ч
      })
      .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt)); // От новых к старым

    this.currentQueue = [...filteredNews];
    this.totalCount = this.currentQueue.length;
    this.processedCount = 0;
    this.startTime = Date.now();

    console.log('\n🚀 SEQUENTIAL NEWS ANALYSIS STARTED');
    console.log('=' .repeat(80));
    console.log(`📊 Total news to analyze: ${this.totalCount}`);
    console.log(`⏱️  Analysis interval: ${this.analysisInterval / 1000 / 60} minutes`);
    console.log(`📅 Time range: Last 24 hours`);
    console.log(`🔄 Order: Newest to oldest`);
    console.log('=' .repeat(80));

    this.emit('analysisStarted', {
      totalCount: this.totalCount,
      interval: this.analysisInterval
    });

    // Запускаем обработку
    this.processNextNews();
  }

  // Обработка следующей новости в очереди
  async processNextNews() {
    if (!this.isRunning || this.currentQueue.length === 0) {
      this.completeAnalysis();
      return;
    }

    const news = this.currentQueue.shift();
    this.processedCount++;

    const progress = ((this.processedCount / this.totalCount) * 100).toFixed(1);
    const remaining = this.currentQueue.length;
    const eta = this.calculateETA();

    console.log('\n' + '─'.repeat(80));
    console.log(`🔍 ANALYZING NEWS ${this.processedCount}/${this.totalCount} (${progress}%)`);
    console.log(`📰 Title: ${news.title?.slice(0, 70)}...`);
    console.log(`📅 Published: ${new Date(news.publishedAt).toLocaleString()}`);
    console.log(`📊 Remaining: ${remaining} | ETA: ${eta}`);
    console.log('─'.repeat(80));

    const analysisStartTime = Date.now();

    try {
      // Подробный лог начала анализа
      this.logAnalysisStart(news);

      // Анализируем новость
      this.currentAnalysis = news;
      const result = await analyzeNews(news);

      const analysisTime = Date.now() - analysisStartTime;

      // 🔍 ПРОВЕРЯЕМ РЕЗУЛЬТАТ АНАЛИЗА
      if (!result) {
        console.log(`[SKIP] ⏭️  Новость пропущена (короткий контент): ${news.title?.slice(0, 50)}...`);
        this.stats.errors++; // Считаем как пропущенную

        // Переходим к следующей новости без сохранения
        this.scheduleNextAnalysis();
        return;
      }

      this.updateStats(result, analysisTime);

      // Подробный лог результата
      this.logAnalysisResult(result, analysisTime);

      // НОВАЯ ЛОГИКА: Сначала кэш, потом лента
      console.log(`[CACHE] 💾 Сохраняем проанализированную новость в кэш`);

      // Добавляем время сохранения в кэш
      result.cachedAt = new Date().toISOString();

      // Уведомляем о прогрессе (это обновит кэш)
      this.emit('newsAnalyzed', {
        news: result,
        progress: this.processedCount / this.totalCount,
        remaining: remaining,
        stats: this.stats
      });

      // Отправляем обновление клиентам (лента)
      console.log(`[FEED] 📡 Отправляем новость в ленту клиентам`);
      if (this.broadcastCallback) {
        this.broadcastCallback(result, 'news-analyzed');
        console.log(`[FEED] ✅ Новость отправлена клиентам`);
      }

    } catch (error) {
      this.stats.errors++;
      console.log(`❌ ANALYSIS FAILED: ${error.message}`);
      
      this.emit('analysisError', {
        news,
        error: error.message,
        progress: this.processedCount / this.totalCount
      });
    }

    this.currentAnalysis = null;

    // Планируем следующий анализ
    this.scheduleNextAnalysis();
  }

  // Планирование следующего анализа
  scheduleNextAnalysis() {
    if (this.currentQueue.length > 0) {
      console.log(`\n⏳ Waiting ${this.analysisInterval / 1000 / 60} minutes before next analysis...`);
      console.log(`🎯 Next: ${this.currentQueue[0].title?.slice(0, 50)}...`);

      setTimeout(() => {
        this.processNextNews();
      }, this.analysisInterval);
    } else {
      this.completeAnalysis();
    }
  }

  // Подробный лог начала анализа
  logAnalysisStart(news) {
    console.log('\n🤖 AI AGENT REASONING PROCESS:');
    console.log('┌' + '─'.repeat(78) + '┐');
    console.log(`│ 📝 Source: ${(news.source || 'Unknown').padEnd(66)} │`);
    console.log(`│ 🔗 URL: ${(news.url?.slice(0, 69) || 'N/A').padEnd(69)} │`);
    console.log(`│ 📏 Content length: ${(news.content?.length || 0).toString().padEnd(58)} │`);
    console.log('├' + '─'.repeat(78) + '┤');
    console.log('│ 🧠 AI is now analyzing:                                               │');
    console.log('│   • Market context and current conditions                             │');
    console.log('│   • Historical patterns and similar events                           │');
    console.log('│   • Sentiment indicators and concrete evidence                       │');
    console.log('│   • Potential market impact and correlations                         │');
    console.log('└' + '─'.repeat(78) + '┘');
  }

  // Подробный лог результата анализа
  logAnalysisResult(result, analysisTime) {
    const sentiment = result.sentiment || 'unknown';
    const score = result.sentimentAnalysis?.sentimentScore || 0;
    const justification = result.sentimentAnalysis?.sentimentJustification || 'No justification provided';
    
    console.log('\n✅ AI ANALYSIS COMPLETE:');
    console.log('┌' + '─'.repeat(78) + '┐');
    console.log(`│ 🎯 SENTIMENT: ${sentiment.toUpperCase().padEnd(63)} │`);
    console.log(`│ 📊 SCORE: ${score.toString().padEnd(67)} │`);
    console.log(`│ ⏱️  TIME: ${(analysisTime / 1000).toFixed(1)}s`.padEnd(78) + '│');
    console.log('├' + '─'.repeat(78) + '┤');
    console.log('│ 💭 AI JUSTIFICATION:                                                  │');
    
    // Разбиваем обоснование на строки
    const maxWidth = 74;
    const words = justification.split(' ');
    let currentLine = '';
    
    for (const word of words) {
      if ((currentLine + word).length <= maxWidth) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          console.log(`│ ${currentLine.padEnd(76)} │`);
        }
        currentLine = word;
      }
    }
    if (currentLine) {
      console.log(`│ ${currentLine.padEnd(76)} │`);
    }
    
    console.log('├' + '─'.repeat(78) + '┤');
    
    // Показываем контекстуальную информацию
    if (result.marketContext) {
      console.log(`│ 📊 Market: ${result.marketContext.marketCondition} | Volatility: ${result.marketContext.volatility}`.padEnd(78) + '│');
    }
    
    if (result.historicalContext?.hasContext) {
      console.log(`│ 🧠 History: ${result.historicalContext.similarEventsCount} similar events found`.padEnd(78) + '│');
    }
    
    console.log('└' + '─'.repeat(78) + '┘');
  }

  // Обновление статистики
  updateStats(result, analysisTime) {
    this.stats.analyzed++;
    this.stats.totalTime += analysisTime;
    this.stats.averageTime = this.stats.totalTime / this.stats.analyzed;

    const sentiment = result.sentiment || 'neutral';
    this.stats[sentiment]++;
  }

  // Расчет ETA
  calculateETA() {
    if (this.processedCount === 0) return 'Calculating...';
    
    const elapsed = Date.now() - this.startTime;
    const avgTimePerNews = elapsed / this.processedCount;
    const remaining = this.currentQueue.length;
    const etaMs = remaining * avgTimePerNews;
    
    const hours = Math.floor(etaMs / (1000 * 60 * 60));
    const minutes = Math.floor((etaMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  // Завершение анализа
  completeAnalysis() {
    this.isRunning = false;
    const totalTime = Date.now() - this.startTime;
    
    console.log('\n🎉 SEQUENTIAL ANALYSIS COMPLETED!');
    console.log('=' .repeat(80));
    console.log(`📊 FINAL STATISTICS:`);
    console.log(`   • Total analyzed: ${this.stats.analyzed}`);
    console.log(`   • Positive: ${this.stats.positive} (${(this.stats.positive / this.stats.analyzed * 100).toFixed(1)}%)`);
    console.log(`   • Neutral: ${this.stats.neutral} (${(this.stats.neutral / this.stats.analyzed * 100).toFixed(1)}%)`);
    console.log(`   • Negative: ${this.stats.negative} (${(this.stats.negative / this.stats.analyzed * 100).toFixed(1)}%)`);
    console.log(`   • Errors: ${this.stats.errors}`);
    console.log(`   • Average analysis time: ${(this.stats.averageTime / 1000).toFixed(1)}s`);
    console.log(`   • Total time: ${(totalTime / 1000 / 60).toFixed(1)} minutes`);
    console.log('=' .repeat(80));

    this.emit('analysisCompleted', {
      stats: this.stats,
      totalTime: totalTime
    });
  }

  // Остановка анализа
  stop() {
    if (this.isRunning) {
      this.isRunning = false;
      console.log('\n🛑 Sequential analysis stopped by user');
      
      this.emit('analysisStopped', {
        processed: this.processedCount,
        remaining: this.currentQueue.length,
        stats: this.stats
      });
    }
  }

  // Получение текущего статуса
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentNews: this.currentAnalysis,
      progress: this.totalCount > 0 ? this.processedCount / this.totalCount : 0,
      processed: this.processedCount,
      total: this.totalCount,
      remaining: this.currentQueue.length,
      stats: this.stats,
      eta: this.calculateETA()
    };
  }
}

// Создаем единственный экземпляр
const sequentialAnalyzer = new SequentialNewsAnalyzer();

module.exports = {
  SequentialNewsAnalyzer,
  sequentialAnalyzer
};
