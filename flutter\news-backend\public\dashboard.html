<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News Analysis Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #4CAF50; }
        .status-stopped { background: #f44336; }
        .status-waiting { background: #ff9800; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #888;
        }
        
        .log-info { color: #00ff00; }
        .log-warning { color: #ffaa00; }
        .log-error { color: #ff4444; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .pulsing {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI News Analysis Dashboard</h1>
            <p>Real-time monitoring of sequential news analysis with market data & contextual memory</p>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>📊 Analysis Status</h3>
                <div id="status-display">
                    <span class="status-indicator status-stopped"></span>
                    <span id="status-text">Stopped</span>
                </div>
                <div id="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="progress-text">0% complete</div>
                </div>
                <div id="current-analysis" style="margin-top: 15px; display: none;">
                    <strong>Currently analyzing:</strong>
                    <div id="current-news" style="font-size: 0.9rem; color: #666; margin-top: 5px;"></div>
                </div>
            </div>
            
            <div class="card">
                <h3>📈 Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="stat-analyzed">0</div>
                        <div class="stat-label">Analyzed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-positive">0</div>
                        <div class="stat-label">Positive</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-neutral">0</div>
                        <div class="stat-label">Neutral</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="stat-negative">0</div>
                        <div class="stat-label">Negative</div>
                    </div>
                </div>
                <div style="margin-top: 15px; text-align: center;">
                    <div><strong>ETA:</strong> <span id="eta">-</span></div>
                    <div><strong>Avg Time:</strong> <span id="avg-time">-</span></div>
                </div>
            </div>
            
            <div class="card">
                <h3>🧠 Memory & Market</h3>
                <div id="memory-stats">
                    <div><strong>Historical Events:</strong> <span id="memory-events">-</span></div>
                    <div><strong>Patterns:</strong> <span id="memory-patterns">-</span></div>
                    <div><strong>Market Condition:</strong> <span id="market-condition">-</span></div>
                    <div><strong>Volatility:</strong> <span id="market-volatility">-</span></div>
                    <div><strong>Data Source:</strong> <span id="market-source">-</span></div>
                    <div><strong>Rate Limiting:</strong> <span id="rate-limit-status" style="color: #4CAF50;">✅ Active</span></div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" id="start-btn" onclick="startAnalysis()">🚀 Start Sequential Analysis</button>
            <button class="btn btn-danger" id="stop-btn" onclick="stopAnalysis()" disabled>🛑 Stop Analysis</button>
            <button class="btn" onclick="refreshStatus()">🔄 Refresh Status</button>
        </div>
        
        <div class="card">
            <h3>📝 Live Analysis Log</h3>
            <div class="log-container" id="log-container">
                <div class="log-entry log-info">
                    <span class="log-timestamp">[SYSTEM]</span> Dashboard initialized. Ready for analysis.
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            loadMemoryStats();
            loadMarketData();
            
            // Обновляем статус каждые 5 секунд
            updateInterval = setInterval(refreshStatus, 5000);
        });
        
        async function startAnalysis() {
            try {
                addLog('Starting sequential analysis...', 'info');
                const response = await fetch('/admin/analysis/start-sequential', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.status === 'success') {
                    addLog('Sequential analysis started successfully!', 'info');
                    document.getElementById('start-btn').disabled = true;
                    document.getElementById('stop-btn').disabled = false;
                } else {
                    addLog(`Error: ${data.message}`, 'error');
                }
            } catch (error) {
                addLog(`Failed to start analysis: ${error.message}`, 'error');
            }
        }
        
        async function stopAnalysis() {
            try {
                addLog('Stopping analysis...', 'warning');
                const response = await fetch('/admin/analysis/stop', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.status === 'success') {
                    addLog('Analysis stopped successfully!', 'warning');
                    document.getElementById('start-btn').disabled = false;
                    document.getElementById('stop-btn').disabled = true;
                } else {
                    addLog(`Error: ${data.message}`, 'error');
                }
            } catch (error) {
                addLog(`Failed to stop analysis: ${error.message}`, 'error');
            }
        }
        
        async function refreshStatus() {
            try {
                const response = await fetch('/admin/analysis/status');
                const data = await response.json();
                
                if (data.status === 'success') {
                    updateStatusDisplay(data.analysisStatus);
                }
            } catch (error) {
                console.error('Failed to refresh status:', error);
            }
        }
        
        async function loadMemoryStats() {
            try {
                const response = await fetch('/admin/memory/stats');
                const data = await response.json();
                
                if (data.status === 'success') {
                    document.getElementById('memory-events').textContent = data.stats.memorySize;
                    document.getElementById('memory-patterns').textContent = data.stats.patterns;
                }
            } catch (error) {
                console.error('Failed to load memory stats:', error);
            }
        }
        
        async function loadMarketData() {
            try {
                const response = await fetch('/admin/market/data');
                const data = await response.json();
                
                if (data.status === 'success') {
                    document.getElementById('market-condition').textContent = data.context.marketCondition || '-';
                    document.getElementById('market-volatility').textContent = data.context.volatility || '-';

                    // Показываем источник данных
                    const source = data.marketData.source || 'unknown';
                    const sourceElement = document.getElementById('market-source');
                    if (source === 'api_with_fallback') {
                        sourceElement.textContent = '🌐 API + Fallback';
                        sourceElement.style.color = '#4CAF50';
                    } else if (source === 'fallback_only') {
                        sourceElement.textContent = '🔄 Fallback Only';
                        sourceElement.style.color = '#ff9800';
                    } else {
                        sourceElement.textContent = source;
                        sourceElement.style.color = '#666';
                    }
                }
            } catch (error) {
                console.error('Failed to load market data:', error);
            }
        }
        
        function updateStatusDisplay(status) {
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('status-text');
            const progressContainer = document.getElementById('progress-container');
            const currentAnalysisContainer = document.getElementById('current-analysis');
            
            if (status.isRunning) {
                statusIndicator.className = 'status-indicator status-running';
                statusText.textContent = 'Running';
                statusText.classList.add('pulsing');
                
                progressContainer.style.display = 'block';
                const progressPercent = (status.progress * 100).toFixed(1);
                document.getElementById('progress-fill').style.width = progressPercent + '%';
                document.getElementById('progress-text').textContent = `${progressPercent}% complete (${status.processed}/${status.total})`;
                
                if (status.currentNews) {
                    currentAnalysisContainer.style.display = 'block';
                    document.getElementById('current-news').textContent = status.currentNews.title || 'Unknown';
                }
                
                document.getElementById('start-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;
            } else {
                statusIndicator.className = 'status-indicator status-stopped';
                statusText.textContent = 'Stopped';
                statusText.classList.remove('pulsing');
                
                progressContainer.style.display = 'none';
                currentAnalysisContainer.style.display = 'none';
                
                document.getElementById('start-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
            }
            
            // Обновляем статистику
            if (status.stats) {
                document.getElementById('stat-analyzed').textContent = status.stats.analyzed;
                document.getElementById('stat-positive').textContent = status.stats.positive;
                document.getElementById('stat-neutral').textContent = status.stats.neutral;
                document.getElementById('stat-negative').textContent = status.stats.negative;
                document.getElementById('eta').textContent = status.eta || '-';
                document.getElementById('avg-time').textContent = status.stats.averageTime ? 
                    (status.stats.averageTime / 1000).toFixed(1) + 's' : '-';
            }
        }
        
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Ограничиваем количество логов
            const logs = logContainer.children;
            if (logs.length > 100) {
                logContainer.removeChild(logs[0]);
            }
        }
    </script>
</body>
</html>
