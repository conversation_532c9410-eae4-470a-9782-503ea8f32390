const axios = require('axios');
const NodeCache = require('node-cache');
require('dotenv').config();

// Кэш для рыночных данных (обновляется каждые 30 секунд)
const marketCache = new NodeCache({ stdTTL: 30 });

// Основные криптовалюты для отслеживания
const MAJOR_CRYPTOS = [
  'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
  'polkadot', 'dogecoin', 'avalanche-2', 'chainlink', 'polygon'
];

// Основные фондовые индексы
const MAJOR_INDICES = ['SPY', 'QQQ', 'DIA', 'IWM', 'VTI'];

class MarketDataService {
  constructor() {
    this.priceHistory = new Map(); // Хранит историю цен для анализа
    this.anomalyThreshold = 0.05; // 5% изменение считается значительным
    this.updateInterval = null;

    // Rate limiting настройки
    this.lastCryptoRequest = 0;
    this.lastStockRequest = 0;
    this.cryptoRateLimit = 60000; // 1 минута между запросами к CoinGecko
    this.stockRateLimit = 30000; // 30 секунд между запросами к Yahoo
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 секунд базовая задержка

    // Fallback данные
    this.fallbackData = {
      crypto: {
        bitcoin: { usd: 105000, usd_24h_change: 0.5, usd_24h_vol: 25000000000 },
        ethereum: { usd: 3800, usd_24h_change: 1.2, usd_24h_vol: 15000000000 },
        binancecoin: { usd: 650, usd_24h_change: -0.8, usd_24h_vol: 2000000000 }
      },
      stocks: {
        SPY: { price: 580, change: 2.5, changePercent: 0.43, volume: 45000000 },
        QQQ: { price: 520, change: 1.8, changePercent: 0.35, volume: 35000000 }
      }
    };
  }

  // Запуск мониторинга рыночных данных
  startMonitoring() {
    console.log('[MARKET] Starting conservative market monitoring...');
    console.log('[MARKET] Update interval: 5 minutes (to respect API limits)');

    // Первоначальная загрузка данных с задержкой
    setTimeout(() => {
      this.updateMarketData();
    }, 2000);

    // Обновление каждые 5 минут (более консервативно)
    this.updateInterval = setInterval(() => {
      this.updateMarketData();
    }, 5 * 60 * 1000); // 5 минут
  }

  // Остановка мониторинга
  stopMonitoring() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      console.log('[MARKET] Market monitoring stopped');
    }
  }

  // Вспомогательная функция для задержки
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Retry logic с экспоненциальной задержкой
  async retryRequest(requestFn, retries = this.maxRetries) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        if (attempt === retries) {
          throw error; // Последняя попытка - пробрасываем ошибку
        }

        const delay = this.retryDelay * Math.pow(2, attempt - 1); // Экспоненциальная задержка
        console.log(`[MARKET] ⚠️  Request failed (attempt ${attempt}/${retries}), retrying in ${delay/1000}s: ${error.message}`);
        await this.sleep(delay);
      }
    }
  }

  // Обновление рыночных данных
  async updateMarketData() {
    console.log('[MARKET] 🔄 Starting market data update...');

    try {
      // Последовательно получаем данные с задержками
      console.log('[MARKET] 📊 Fetching crypto data...');
      const cryptoData = await this.fetchCryptoData();

      // Задержка между запросами
      await this.sleep(2000);

      console.log('[MARKET] 📈 Fetching stock data...');
      const stockData = await this.fetchStockData();

      const currentData = {
        crypto: cryptoData || this.fallbackData.crypto,
        stocks: stockData || this.fallbackData.stocks,
        timestamp: Date.now(),
        source: 'api_with_fallback'
      };

      // Анализируем изменения
      const anomalies = this.detectAnomalies(currentData);

      // Сохраняем в кэш
      marketCache.set('current_data', currentData);
      marketCache.set('anomalies', anomalies);

      // Обновляем историю
      this.updatePriceHistory(currentData);

      console.log(`[MARKET] ✅ Market data updated successfully`);
      console.log(`[MARKET] 📊 Crypto assets: ${Object.keys(currentData.crypto).length}`);
      console.log(`[MARKET] 📈 Stock indices: ${Object.keys(currentData.stocks).length}`);

      if (anomalies.length > 0) {
        console.log(`[MARKET] 🚨 Detected ${anomalies.length} market anomalies`);
        anomalies.forEach(anomaly => {
          console.log(`[MARKET]   - ${anomaly.asset}: ${anomaly.change.toFixed(2)}% (${anomaly.severity})`);
        });
      }

    } catch (error) {
      console.error('[MARKET] ❌ Critical error updating market data:', error.message);

      // В случае критической ошибки используем fallback данные
      const fallbackData = {
        crypto: this.fallbackData.crypto,
        stocks: this.fallbackData.stocks,
        timestamp: Date.now(),
        source: 'fallback_only'
      };

      marketCache.set('current_data', fallbackData);
      marketCache.set('anomalies', []);

      console.log('[MARKET] 🔄 Using complete fallback data set');
    }
  }

  // Получение данных по криптовалютам с rate limiting
  async fetchCryptoData() {
    try {
      // Проверяем rate limit
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastCryptoRequest;

      if (timeSinceLastRequest < this.cryptoRateLimit) {
        const waitTime = this.cryptoRateLimit - timeSinceLastRequest;
        console.log(`[MARKET] ⏱️  Rate limit: waiting ${Math.round(waitTime/1000)}s before crypto API request`);
        await this.sleep(waitTime);
      }

      this.lastCryptoRequest = Date.now();

      const response = await this.retryRequest(async () => {
        return await axios.get('https://api.coingecko.com/api/v3/simple/price', {
          params: {
            ids: MAJOR_CRYPTOS.slice(0, 5).join(','), // Ограничиваем до 5 криптовалют
            vs_currencies: 'usd',
            include_24hr_change: true,
            include_24hr_vol: true,
            include_market_cap: true
          },
          timeout: 15000,
          headers: {
            'User-Agent': 'NewsAnalyzer/1.0'
          }
        });
      });

      console.log(`[MARKET] ✅ Successfully fetched crypto data for ${Object.keys(response.data).length} assets`);
      return response.data;

    } catch (error) {
      console.error('[MARKET] ❌ Error fetching crypto data:', error.message);
      console.log('[MARKET] 🔄 Using fallback crypto data');
      return this.fallbackData.crypto;
    }
  }

  // Получение данных по акциям с rate limiting
  async fetchStockData() {
    try {
      // Проверяем rate limit
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastStockRequest;

      if (timeSinceLastRequest < this.stockRateLimit) {
        const waitTime = this.stockRateLimit - timeSinceLastRequest;
        console.log(`[MARKET] ⏱️  Rate limit: waiting ${Math.round(waitTime/1000)}s before stock API request`);
        await this.sleep(waitTime);
      }

      this.lastStockRequest = Date.now();
      const stockData = {};

      // Ограничиваем до 2 индексов для снижения нагрузки
      for (const symbol of MAJOR_INDICES.slice(0, 2)) {
        try {
          await this.sleep(1000); // 1 секунда между запросами

          const response = await this.retryRequest(async () => {
            return await axios.get(`https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`, {
              timeout: 10000,
              headers: {
                'User-Agent': 'NewsAnalyzer/1.0'
              }
            });
          });

          const result = response.data.chart.result[0];
          const meta = result.meta;
          const quote = result.indicators.quote[0];

          stockData[symbol] = {
            price: meta.regularMarketPrice,
            change: meta.regularMarketPrice - meta.previousClose,
            changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
            volume: quote.volume[quote.volume.length - 1]
          };

          console.log(`[MARKET] ✅ Fetched data for ${symbol}: $${meta.regularMarketPrice}`);

        } catch (err) {
          console.error(`[MARKET] ❌ Error fetching ${symbol}:`, err.message);
          // Используем fallback данные для этого символа
          if (this.fallbackData.stocks[symbol]) {
            stockData[symbol] = this.fallbackData.stocks[symbol];
            console.log(`[MARKET] 🔄 Using fallback data for ${symbol}`);
          }
        }
      }

      return stockData;
    } catch (error) {
      console.error('[MARKET] ❌ Error fetching stock data:', error.message);
      console.log('[MARKET] 🔄 Using fallback stock data');
      return this.fallbackData.stocks;
    }
  }

  // Детектор аномальных движений рынка
  detectAnomalies(currentData) {
    const anomalies = [];
    const previousData = marketCache.get('current_data');
    
    if (!previousData) return anomalies;

    // Анализ криптовалют
    for (const [crypto, data] of Object.entries(currentData.crypto)) {
      if (data.usd_24h_change && Math.abs(data.usd_24h_change) > this.anomalyThreshold * 100) {
        anomalies.push({
          type: 'crypto',
          asset: crypto,
          change: data.usd_24h_change,
          price: data.usd,
          severity: this.calculateSeverity(data.usd_24h_change),
          timestamp: currentData.timestamp
        });
      }
    }

    // Анализ акций
    for (const [stock, data] of Object.entries(currentData.stocks)) {
      if (data.changePercent && Math.abs(data.changePercent) > this.anomalyThreshold * 100) {
        anomalies.push({
          type: 'stock',
          asset: stock,
          change: data.changePercent,
          price: data.price,
          severity: this.calculateSeverity(data.changePercent),
          timestamp: currentData.timestamp
        });
      }
    }

    return anomalies;
  }

  // Расчет серьезности аномалии
  calculateSeverity(changePercent) {
    const absChange = Math.abs(changePercent);
    if (absChange > 20) return 'critical';
    if (absChange > 10) return 'high';
    if (absChange > 5) return 'medium';
    return 'low';
  }

  // Обновление истории цен
  updatePriceHistory(currentData) {
    const timestamp = currentData.timestamp;
    
    // Сохраняем историю для криптовалют
    for (const [crypto, data] of Object.entries(currentData.crypto)) {
      if (!this.priceHistory.has(crypto)) {
        this.priceHistory.set(crypto, []);
      }
      
      const history = this.priceHistory.get(crypto);
      history.push({
        timestamp,
        price: data.usd,
        volume: data.usd_24h_vol,
        change: data.usd_24h_change
      });
      
      // Ограничиваем историю последними 1000 записями
      if (history.length > 1000) {
        history.shift();
      }
    }

    // Сохраняем историю для акций
    for (const [stock, data] of Object.entries(currentData.stocks)) {
      if (!this.priceHistory.has(stock)) {
        this.priceHistory.set(stock, []);
      }
      
      const history = this.priceHistory.get(stock);
      history.push({
        timestamp,
        price: data.price,
        volume: data.volume,
        change: data.changePercent
      });
      
      if (history.length > 1000) {
        history.shift();
      }
    }
  }

  // Получение текущих рыночных данных
  getCurrentMarketData() {
    return marketCache.get('current_data') || {};
  }

  // Получение аномалий
  getCurrentAnomalies() {
    return marketCache.get('anomalies') || [];
  }

  // Получение истории цен для актива
  getPriceHistory(asset, limit = 100) {
    const history = this.priceHistory.get(asset) || [];
    return history.slice(-limit);
  }

  // Корреляционный анализ новости с рыночными движениями
  analyzeNewsMarketCorrelation(newsTimestamp, affectedAssets = []) {
    const correlations = [];
    const newsTime = new Date(newsTimestamp).getTime();
    const timeWindow = 60 * 60 * 1000; // 1 час до и после новости

    for (const asset of affectedAssets) {
      const history = this.getPriceHistory(asset, 200);
      
      // Находим цены до и после новости
      const beforeNews = history.filter(h => 
        h.timestamp >= newsTime - timeWindow && h.timestamp <= newsTime
      );
      const afterNews = history.filter(h => 
        h.timestamp >= newsTime && h.timestamp <= newsTime + timeWindow
      );

      if (beforeNews.length > 0 && afterNews.length > 0) {
        const priceBefore = beforeNews[beforeNews.length - 1].price;
        const priceAfter = afterNews[afterNews.length - 1].price;
        const priceChange = ((priceAfter - priceBefore) / priceBefore) * 100;

        correlations.push({
          asset,
          priceBefore,
          priceAfter,
          priceChange,
          correlation: this.calculateCorrelationStrength(priceChange)
        });
      }
    }

    return correlations;
  }

  // Расчет силы корреляции
  calculateCorrelationStrength(priceChange) {
    const absChange = Math.abs(priceChange);
    if (absChange > 10) return 'strong';
    if (absChange > 5) return 'medium';
    if (absChange > 2) return 'weak';
    return 'none';
  }

  // Получение рыночного контекста для новости
  getMarketContext() {
    const currentData = this.getCurrentMarketData();
    const anomalies = this.getCurrentAnomalies();
    
    return {
      marketCondition: this.assessMarketCondition(currentData),
      volatility: this.assessVolatility(currentData),
      anomalies: anomalies,
      timestamp: currentData.timestamp
    };
  }

  // Оценка состояния рынка
  assessMarketCondition(data) {
    if (!data.crypto) return 'unknown';
    
    const cryptoChanges = Object.values(data.crypto)
      .map(c => c.usd_24h_change)
      .filter(c => c !== undefined);
    
    if (cryptoChanges.length === 0) return 'unknown';
    
    const avgChange = cryptoChanges.reduce((sum, c) => sum + c, 0) / cryptoChanges.length;
    
    if (avgChange > 5) return 'bullish';
    if (avgChange < -5) return 'bearish';
    return 'neutral';
  }

  // Оценка волатильности
  assessVolatility(data) {
    if (!data.crypto) return 'unknown';
    
    const cryptoChanges = Object.values(data.crypto)
      .map(c => Math.abs(c.usd_24h_change || 0));
    
    if (cryptoChanges.length === 0) return 'unknown';
    
    const avgVolatility = cryptoChanges.reduce((sum, c) => sum + c, 0) / cryptoChanges.length;
    
    if (avgVolatility > 10) return 'high';
    if (avgVolatility > 5) return 'medium';
    return 'low';
  }
}

// Создаем единственный экземпляр сервиса
const marketDataService = new MarketDataService();

module.exports = {
  MarketDataService,
  marketDataService
};
