const axios = require('axios');
require('dotenv').config();

const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_URL = 'https://api.deepseek.com/v1/chat/completions';
const MAX_TOKENS = 8192;

// Ваш готовый промпт для финансового анализа
const FINANCIAL_ANALYSIS_PROMPT = `
// ИНИЦИАЛИЗАЦИЯ СИСТЕМНЫХ ПАРАМЕТРОВ
Ты - продвинутая модель глубокого обучения, специализирующаяся на финансовом анализе.
Твоя задача - провести многоуровневый анализ предоставленной финансовой новости
и спрогнозировать её влияние на криптовалютный и/или фондовый рынок с высокой точностью.

РОЛЬ: ФИНАНСОВЫЙ АНАЛИТИК ЭКСПЕРТ-УРОВНЯ
ОПЫТ: 25+ лет опыта в анализе финансовых рынков
СПЕЦИАЛИЗАЦИЯ: Прогнозирование рыночных трендов на основе семантического анализа новостей

АЛГОРИТМ АНАЛИЗА:
1. Tier-1: Официальные заявления регуляторов, пресс-релизы компаний, судебные решения
2. Tier-2: Крупные финансовые медиа (Bloomberg, WSJ, Reuters, Financial Times)
3. Tier-3: Специализированные криптомедиа высокой репутации (CoinDesk, The Block)
4. Tier-4: Другие источники и социальные медиа

ЭВРИСТИКИ ВЗВЕШИВАНИЯ ФАКТОРОВ:
- Новости регуляторного характера имеют приоритет x1.5
- Мнения ключевых лидеров индустрии (K-list) имеют дополнительный вес x1.3
- Прогнозы, основанные на техническом анализе без фундаментальных факторов, имеют пониженный вес x0.7

ПОРОГОВЫЕ ЗНАЧЕНИЯ:
- HIGH_IMPACT_PROBABILITY: 0.7
- CRYPTO SHORT_TERM: 5%, MEDIUM_TERM: 15%, LONG_TERM: 30%
- STOCKS SHORT_TERM: 2%, MEDIUM_TERM: 5%, LONG_TERM: 10%
- CREDIBILITY_THRESHOLD: 0.65

ОСОБЫЕ ПРАВИЛА АНАЛИЗА:
1. Если новость содержит долгосрочный ценовой прогноз (>1 года), классифицировать как долгосрочное влияние
2. Если новость содержит квантифицированный позитивный прогноз цены, классифицировать как позитивное направление
3. Если источником новости является регуляторный орган, минимальная сила влияния = умеренное
4. При анализе криптовалютных новостей учитывать корреляцию с BTC как системного бенчмарка
5. Взвешивать новости о регулировании на коэффициент x1.5

ФОРМАТ ВЫВОДА - строго JSON:
{
  "summary": "Краткое содержание новости (3-5 предложений)",
  "marketImpact": {
    "strength": "высокое|умеренное|низкое",
    "direction": "позитивное|негативное|нейтральное",
    "rationale": "Логическое обоснование оценки влияния с опорой на факты из новости",
    "affectedAssets": [
      {
        "ticker": "BTC",
        "type": "crypto|stock",
        "impact": "Описание конкретного влияния на этот актив",
        "impactMagnitude": "1-10"
      }
    ],
    "timeHorizon": "краткосрочное|среднесрочное|долгосрочное"
  },
  "forecast": {
    "baseScenario": "Описание наиболее вероятного сценария с указанием конкретных цифр и временных рамок",
    "alternativeScenario": "Описание альтернативного сценария с указанием условий",
    "technicalLevels": {
      "support": ["Уровни поддержки для ключевых активов"],
      "resistance": ["Уровни сопротивления для ключевых активов"]
    }
  },
  "interestingFact": "Релевантный интересный факт, связанный с темой новости"
}

ВАЖНО: 
- Ответ ДОЛЖЕН быть строго в формате JSON
- Поле affectedAssets ОБЯЗАТЕЛЬНО должно содержать не менее 3 конкретных активов
- Для криптовалютных новостей среди затронутых активов всегда оценивать влияние на BTC и ETH
- Все текстовые поля должны быть на русском языке
- В полях rationale обязательно приводить конкретные факты из новости
`;

async function analyzeNewsFinancially(newsItem) {
  try {
    console.log(`[FINANCIAL_AI] Начинаем финансовый анализ: ${newsItem.title?.slice(0, 60)}...`);
    
    // Подготавливаем данные новости для анализа
    const newsData = {
      title: newsItem.title || 'Без заголовка',
      description: newsItem.description || 'Недоступно',
      content: newsItem.content || newsItem.description || 'Недоступно',
      sentiment: newsItem.sentiment || 'Не определено',
      category: newsItem.category || 'Не классифицировано',
      source: newsItem.source || 'Неизвестно',
      publishDate: newsItem.publishedAt || new Date().toISOString()
    };

    // Формируем запрос к AI
    const userPrompt = `
ИСХОДНЫЕ ДАННЫЕ ДЛЯ АНАЛИЗА:
Заголовок: ${newsData.title}
Описание: ${newsData.description}
Содержание: ${newsData.content.slice(0, 4000)} // Ограничиваем длину
Источник: ${newsData.source}
Дата публикации: ${newsData.publishDate}
Текущий сентимент: ${newsData.sentiment}
Категория: ${newsData.category}

Проведи глубокий финансовый анализ этой новости согласно указанной методологии и верни результат в строгом JSON формате.
`;

    const response = await axios.post(DEEPSEEK_URL, {
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: FINANCIAL_ANALYSIS_PROMPT },
        { role: 'user', content: userPrompt },
      ],
      temperature: 0.3,
      max_tokens: MAX_TOKENS,
    }, {
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      timeout: 60000,
    });

    if (response.status !== 200) {
      throw new Error(`API вернул статус ${response.status}: ${JSON.stringify(response.data)}`);
    }

    const content = response.data.choices[0].message.content;
    
    // Парсим JSON ответ
    let analysisResult = {};
    try {
      const jsonStart = content.indexOf('{');
      const jsonEnd = content.lastIndexOf('}');
      if (jsonStart !== -1 && jsonEnd !== -1) {
        analysisResult = JSON.parse(content.slice(jsonStart, jsonEnd + 1));
      } else {
        throw new Error('JSON не найден в ответе');
      }
    } catch (parseError) {
      console.error('[FINANCIAL_AI] Ошибка парсинга JSON:', parseError.message);
      // Fallback результат
      analysisResult = {
        summary: "Не удалось провести полный анализ из-за ошибки парсинга ответа AI",
        marketImpact: {
          strength: "низкое",
          direction: "нейтральное",
          rationale: "Анализ не завершен из-за технической ошибки",
          affectedAssets: [
            { ticker: "BTC", type: "crypto", impact: "Минимальное влияние", impactMagnitude: "2" },
            { ticker: "ETH", type: "crypto", impact: "Минимальное влияние", impactMagnitude: "2" },
            { ticker: "SPY", type: "stock", impact: "Минимальное влияние", impactMagnitude: "1" }
          ],
          timeHorizon: "краткосрочное"
        },
        forecast: {
          baseScenario: "Из-за технической ошибки полный прогноз недоступен",
          alternativeScenario: "Рекомендуется повторить анализ",
          technicalLevels: {
            support: ["Данные недоступны"],
            resistance: ["Данные недоступны"]
          }
        },
        interestingFact: "Финансовые рынки требуют постоянного мониторинга и анализа для принятия обоснованных решений"
      };
    }

    // Добавляем метаданные
    analysisResult.analyzedAt = new Date().toISOString();
    analysisResult.newsId = newsItem.id;
    analysisResult.analysisType = 'financial';

    console.log(`[FINANCIAL_AI] ✅ Финансовый анализ завершен: ${newsItem.title?.slice(0, 60)}...`);
    return analysisResult;

  } catch (error) {
    console.error(`[FINANCIAL_AI] ❌ Ошибка финансового анализа:`, error.message);
    
    // Возвращаем fallback результат при ошибке
    return {
      summary: "Не удалось провести финансовый анализ из-за технической ошибки",
      marketImpact: {
        strength: "низкое",
        direction: "нейтральное", 
        rationale: `Ошибка анализа: ${error.message}`,
        affectedAssets: [
          { ticker: "BTC", type: "crypto", impact: "Анализ недоступен", impactMagnitude: "0" },
          { ticker: "ETH", type: "crypto", impact: "Анализ недоступен", impactMagnitude: "0" },
          { ticker: "SPY", type: "stock", impact: "Анализ недоступен", impactMagnitude: "0" }
        ],
        timeHorizon: "краткосрочное"
      },
      forecast: {
        baseScenario: "Анализ недоступен из-за технической ошибки",
        alternativeScenario: "Попробуйте повторить запрос позже",
        technicalLevels: {
          support: ["Недоступно"],
          resistance: ["Недоступно"]
        }
      },
      interestingFact: "Технические сбои - неотъемлемая часть работы с AI системами",
      analyzedAt: new Date().toISOString(),
      newsId: newsItem.id,
      analysisType: 'financial',
      error: error.message
    };
  }
}

module.exports = { analyzeNewsFinancially };
