import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'register_screen.dart';
import 'login_screen.dart';
import 'hyperjump_animation_screen.dart';
import '../widgets/tmm_png_logo.dart';
import '../widgets/hover_button.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> with SingleTickerProviderStateMixin {
  late AnimationController _starsController;
  final List<Star> _stars = [];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона
    for (int i = 0; i < 100; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 2.0 + 1.0,
        blinkDuration: (math.Random().nextDouble() * 2.0 + 3.0) * 3000, // Замедляем мерцание в 3 раза
      ));
    }

    // Инициализируем контроллер анимации для звезд
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 15000), // Замедляем анимацию в 3 раза (5000 * 3 = 15000)
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _starsController.dispose();
    super.dispose();
  }

  void _navigateToHyperjump(BuildContext context) {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Анимированные звезды на фоне
          AnimatedBuilder(
            animation: _starsController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(size.width, size.height),
                painter: StarsPainter(_stars, _starsController.value),
              );
            },
          ),

          // Эффект размытия (как на экране авторизации)
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: Container(
              color: Colors.black.withAlpha(25), // 0.1 opacity
            ),
          ),

          // Основной контент
          SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Логотип TMM
                  Transform.translate(
                    offset: const Offset(0, -20), // Смещаем логотип на 20 пикселей вверх
                    child: Center(
                      child: TmmPngLogo(
                        size: 244, // Размер логотипа
                      ),
                    ),
                  ).animate().fadeIn(duration: 600.ms).slideY(
                    begin: -0.2,
                    end: 0,
                    curve: Curves.easeOutQuad,
                    duration: 600.ms,
                  ),

                  const SizedBox(height: 24),

                  Text(
                    'TMM',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 38, // Увеличенный размер шрифта
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 2),
                          blurRadius: 4.0,
                          color: Colors.black.withAlpha(150),
                        ),
                      ],
                    ),
                  )
                      .animate()
                      .fadeIn(duration: 600.ms, curve: Curves.easeInOut, delay: 200.ms),

                  const SizedBox(height: 8),

                  Text(
                    'Ваш финансовый помощник',
                    style: TextStyle(
                      fontSize: 20, // Увеличенный размер шрифта
                      color: Colors.white, // Белый цвет для лучшей читаемости
                      fontWeight: FontWeight.w500, // Немного жирнее
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3.0,
                          color: Colors.black.withAlpha(150),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(
                    delay: 400.ms,
                    duration: 600.ms,
                  ),

                  const SizedBox(height: 60),

                  // Кнопка авторизации
                  SizedBox(
                    width: 250,
                    child: HoverButton(
                      text: 'Авторизация',
                      onPressed: () {
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const LoginScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(milliseconds: 500),
                          ),
                        );
                      },
                      backgroundColor: Colors.grey[900]!,
                      hoverColor: Colors.grey[800]!,
                      textColor: Colors.white,
                      fontSize: 18,
                      height: 56,
                      useIosBorder: true,
                      borderColor: Colors.white30,
                      borderWidth: 0.5,
                      borderRadius: BorderRadius.circular(12.5), // Увеличиваем скругление на 25% (10 * 1.25 = 12.5)
                    ),
                  )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                        delay: 600.ms,
                      ),

                  const SizedBox(height: 16),

                  // Кнопка регистрации
                  SizedBox(
                    width: 250,
                    child: HoverButton(
                      text: 'Регистрация',
                      onPressed: () {
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const RegisterScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(milliseconds: 500),
                          ),
                        );
                      },
                      backgroundColor: Colors.grey[800]!,
                      hoverColor: Colors.grey[700]!,
                      textColor: Colors.white,
                      fontSize: 18,
                      height: 56,
                      useIosBorder: true,
                      borderColor: Colors.white30,
                      borderWidth: 0.5,
                      borderRadius: BorderRadius.circular(12.5), // Увеличиваем скругление на 25% (10 * 1.25 = 12.5)
                    ),
                  )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                        delay: 800.ms,
                      ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
  });
}

// Кастомный painter для отрисовки звезд
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white;

    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды
      final opacity = math.max(0.0, math.min(1.0, 0.3 + (0.7 - 0.3) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5)));

      paint.color = Colors.white.withAlpha((opacity * 255).toInt());

      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

